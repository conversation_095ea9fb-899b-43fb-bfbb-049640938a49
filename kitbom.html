<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kit BOM - HCLSoftware</title>

    <!-- Material Design Components -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Export Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/docx/7.1.0/docx.min.js"></script>

    <style>
        /* Modern Design System Variables */
        :root {
            /* White Theme - Clean and Bright */
            --primary-gradient: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            --secondary-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            --accent-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --error-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;

            /* White Theme Neutral Palette */
            --background: #ffffff;
            --surface: #ffffff;
            --surface-elevated: #f8fafc;
            --surface-overlay: rgba(255, 255, 255, 0.98);
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --divider: #e5e7eb;

            /* White Theme Text Colors - All Black */
            --text-primary: #000000;
            --text-secondary: #000000;
            --text-tertiary: #000000;
            --text-disabled: #666666;
            --text-on-primary: #ffffff;
            --text-on-dark: #ffffff;

            /* Compact Layout */
            --sidebar-width: 260px;
            --sidebar-collapsed: 70px;
            --header-height: 60px;
            --content-max-width: 1400px;

            /* Modern Border Radius */
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --radius-full: 9999px;

            /* Modern Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

            /* White Theme Glassmorphism */
            --glass-bg: rgba(255, 255, 255, 0.85);
            --glass-border: rgba(37, 99, 235, 0.15);
            --glass-backdrop: blur(20px);

            /* Compact Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 0.875rem;
            --space-5: 1rem;
            --space-6: 1.125rem;
            --space-8: 1.25rem;
            --space-10: 1.5rem;
            --space-12: 1.75rem;
            --space-16: 2rem;
            --space-20: 2.5rem;

            /* Modern Typography */
            --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            --font-family-mono: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;

            /* Font Sizes - Direct Pixel Values */
            --text-xs: 12px;
            --text-sm: 14px;
            --text-base: 16px;
            --text-lg: 18px;
            --text-xl: 20px;
            --text-2xl: 24px;
            --text-3xl: 30px;
            --text-4xl: 36px;
            --text-5xl: 48px;

            /* Font Weights */
            --font-light: 300;
            --font-normal: 400;
            --font-medium: 500;
            --font-semibold: 600;
            --font-bold: 700;
            --font-extrabold: 800;

            /* Line Heights */
            --leading-tight: 1.25;
            --leading-snug: 1.375;
            --leading-normal: 1.5;
            --leading-relaxed: 1.625;
            --leading-loose: 2;

            /* Transitions */
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 250ms ease-in-out;
            --transition-slow: 350ms ease-in-out;

            /* Z-Index Scale */
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal-backdrop: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-toast: 1080;
        }

        /* Black Theme - Deep and Sophisticated */
        [data-theme="dark"] {
            /* Black Theme Gradients */
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            --secondary-gradient: linear-gradient(135deg, #ec4899 0%, #f43f5e 100%);
            --accent-gradient: linear-gradient(135deg, #14b8a6 0%, #06b6d4 100%);
            --success-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            --warning-gradient: linear-gradient(135deg, #eab308 0%, #ca8a04 100%);
            --error-gradient: linear-gradient(135deg, #f87171 0%, #ef4444 100%);

            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #8b5cf6;
            --secondary-color: #ec4899;
            --accent-color: #14b8a6;
            --success-color: #22c55e;
            --warning-color: #eab308;
            --error-color: #f87171;

            /* Black Theme Neutral Palette */
            --background: #000000;
            --surface: #0a0a0a;
            --surface-elevated: #171717;
            --surface-overlay: rgba(10, 10, 10, 0.98);
            --border-color: #262626;
            --border-light: #171717;
            --divider: #262626;

            /* Black Theme Text Colors */
            --text-primary: #ffffff;
            --text-secondary: #d4d4d8;
            --text-tertiary: #a1a1aa;
            --text-disabled: #71717a;
            --text-on-primary: #ffffff;
            --text-on-dark: #ffffff;

            /* Black Theme Glassmorphism */
            --glass-bg: rgba(10, 10, 10, 0.85);
            --glass-border: rgba(99, 102, 241, 0.2);
            --glass-backdrop: blur(20px);

            /* Black Theme Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.4);
        }

        /* Import Modern Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

        /* Modern Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        *::before,
        *::after {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-family-primary);
            font-size: var(--text-base);
            font-weight: var(--font-normal);
            line-height: var(--leading-normal);
            color: var(--text-primary);
            background: var(--background);
            transition: all var(--transition-normal);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: auto; /* Allow horizontal scroll when needed */
            min-width: 320px; /* Minimum supported width */
        }

        /* Modern Typography Scale */
        h1, h2, h3, h4, h5, h6 {
            font-weight: var(--font-semibold);
            line-height: var(--leading-tight);
            color: var(--text-primary);
            margin-bottom: var(--space-4);
        }

        h1 { font-size: var(--text-4xl); font-weight: var(--font-bold); }
        h2 { font-size: var(--text-3xl); }
        h3 { font-size: var(--text-2xl); }
        h4 { font-size: var(--text-xl); }
        h5 { font-size: var(--text-lg); }
        h6 { font-size: var(--text-base); }

        p {
            margin-bottom: var(--space-4);
            color: var(--text-secondary);
            line-height: var(--leading-relaxed);
        }

        /* Modern Link Styles */
        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all var(--transition-fast);
            position: relative;
        }

        a:hover {
            color: var(--primary-dark);
        }

        a:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
            border-radius: var(--radius-sm);
        }

        /* Modern Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--background);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: var(--radius-full);
            transition: background var(--transition-fast);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-tertiary);
        }

        /* Sidebar - Hidden scrollbar but functional */
        .sidebar::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: transparent;
        }

        /* Dark theme sidebar scrollbar - hidden */
        [data-theme="dark"] .sidebar::-webkit-scrollbar-thumb {
            background: transparent;
        }

        [data-theme="dark"] .sidebar::-webkit-scrollbar-thumb:hover {
            background: transparent;
        }

        /* Firefox sidebar scrollbar - hidden */
        .sidebar {
            scrollbar-width: none;
            scrollbar-color: transparent transparent;
        }

        [data-theme="dark"] .sidebar {
            scrollbar-color: transparent transparent;
        }

        /* Modern Selection */
        ::selection {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        ::-moz-selection {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        /* Horizontal Scrolling Utilities */
        .horizontal-scroll {
            overflow-x: auto;
            overflow-y: hidden;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
        }

        .horizontal-scroll-container {
            display: flex;
            gap: var(--space-4);
            min-width: max-content;
            padding: var(--space-2) 0;
        }

        .prevent-collapse {
            flex-shrink: 0;
            min-width: 0;
        }

        .stable-width {
            width: 100%;
            min-width: 320px;
            max-width: 100%;
        }

        /* Developer Tools Viewport Detection and Fixes */
        .dev-tools-safe {
            min-width: 320px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .dev-tools-flex {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .dev-tools-flex > * {
            flex-shrink: 0;
        }

        /* Viewport change detection for developer tools */
        @media (max-width: 1400px) and (min-width: 1025px) {
            .main-content {
                padding: var(--space-3);
                min-width: 320px;
                overflow-x: auto;
            }

            .page-actions {
                overflow-x: auto;
                flex-wrap: nowrap;
            }

            .data-header {
                overflow-x: auto;
                flex-wrap: nowrap;
            }

            .data-container {
                min-height: 400px;
                height: calc(100vh - 320px);
                flex-shrink: 0;
            }

            .data-view {
                min-height: 300px;
                flex-shrink: 0;
            }

            /* Prevent sidebar collapse during dev tools */
            .sidebar {
                width: var(--sidebar-width) !important;
                min-width: var(--sidebar-width) !important;
                flex-shrink: 0 !important;
            }

            .sidebar.collapsed {
                width: var(--sidebar-collapsed) !important;
                min-width: var(--sidebar-collapsed) !important;
            }
        }

        /* Responsive Layout Container */
        .app-container {
            display: flex;
            min-height: 100vh;
            background: var(--background);
            position: relative;
            width: 100vw;
            max-width: 100vw;
            overflow-x: hidden;
        }

        /* Responsive breakpoints with developer tools consideration */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
                overflow-x: hidden;
                width: 100vw;
                max-width: 100vw;
            }

            .main-content {
                margin-left: 0;
                padding: var(--space-2);
                width: 100vw;
                max-width: 100vw;
            }

            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: var(--z-modal);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }
        }

        /* Tablet and small desktop - handle developer tools */
        @media (max-width: 1024px) {
            .page-actions {
                overflow-x: auto;
                flex-wrap: nowrap;
            }

            .header-left {
                min-width: 200px;
            }

            .search-container {
                min-width: 150px;
                max-width: 300px;
            }

            .main-content {
                width: calc(100vw - var(--sidebar-width) - var(--space-12));
                max-width: calc(100vw - var(--sidebar-width) - var(--space-12));
            }

            .main-content.sidebar-collapsed {
                width: calc(100vw - var(--sidebar-collapsed) - var(--space-12));
                max-width: calc(100vw - var(--sidebar-collapsed) - var(--space-12));
            }
        }

        /* Developer tools open detection - ensure no collapse */
        @media (max-width: 1200px) and (min-width: 769px) {
            .data-header {
                flex-wrap: nowrap;
                overflow-x: auto;
            }

            .data-controls {
                flex-shrink: 0;
            }

            /* Ensure cards maintain size */
            .kit-card {
                flex: 0 0 320px;
                min-width: 320px;
                max-width: 320px;
                width: 320px;
            }
        }

        /* Modern Header with Glassmorphism */
        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--space-6);
            z-index: 1003;
            transition: all var(--transition-normal);
            min-width: 320px;
            overflow-x: auto;
            flex-shrink: 0;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-6);
            flex: 1;
            min-width: 0;
            flex-shrink: 0;
        }

        /* Mobile Menu Toggle */
        .mobile-menu-toggle {
            display: none;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            background: var(--surface);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .mobile-menu-toggle:hover {
            background: var(--surface-elevated);
            color: var(--text-primary);
            transform: scale(1.05);
        }

        /* Modern Logo with Black Text */
        .logo {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        /* Modern Breadcrumbs */
        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--text-tertiary);
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .breadcrumb-separator {
            color: var(--text-disabled);
        }

        .breadcrumb-current {
            color: var(--text-primary);
            font-weight: var(--font-medium);
        }

        /* Modern Search Container */
        .search-container {
            position: relative;
            width: 100%;
            max-width: 480px;
            min-width: 200px;
            margin: 0 var(--space-8);
            flex-shrink: 1;
        }

        .search-input {
            width: 100%;
            height: 44px;
            padding: 0 var(--space-4) 0 var(--space-12);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-full);
            background: var(--surface);
            color: var(--text-primary);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .search-input::placeholder {
            color: var(--text-tertiary);
            font-weight: var(--font-normal);
        }

        .search-icon {
            position: absolute;
            left: var(--space-4);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
            font-size: 20px;
            pointer-events: none;
        }

        /* Modern Header Right Section */
        .header-right {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            flex-shrink: 0;
            min-width: max-content;
        }

        /* Modern Notification Center */
        .notification-center {
            position: relative;
            cursor: pointer;
        }

        .notification-bell {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: var(--surface);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .notification-bell:hover {
            background: var(--surface-elevated);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background: var(--error-gradient);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-xs);
            font-weight: var(--font-bold);
            color: white;
            border: 2px solid var(--surface);
        }

        /* Modern Theme Toggle */
        .theme-toggle {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: var(--surface);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .theme-toggle:hover {
            background: var(--surface-elevated);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .theme-toggle:active {
            transform: scale(0.95);
        }

        /* Modern User Profile */
        .user-profile {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-full);
            background: var(--surface);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .user-profile:hover {
            background: var(--surface-elevated);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: var(--font-semibold);
            font-size: var(--text-sm);
        }

        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--text-primary);
            line-height: 1.2;
        }

        .user-role {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
            line-height: 1.2;
        }

        /* Modern Floating Sidebar - Stable Navigation */
        .sidebar {
            position: fixed;
            left: var(--space-6);
            top: calc(var(--header-height) + var(--space-6));
            width: var(--sidebar-width);
            min-width: var(--sidebar-width);
            height: calc(100vh - var(--header-height) - var(--space-12));
            min-height: 500px;
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            -webkit-backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            overflow-y: auto;
            overflow-x: hidden;
            z-index: var(--z-sticky);
            transition: all var(--transition-normal);
            flex-shrink: 0;
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed);
            min-width: var(--sidebar-collapsed);
        }

        /* Sidebar Header */
        .sidebar-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--divider);
            background: var(--surface-elevated);
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .sidebar-subtitle {
            font-size: 14px;
            color: var(--text-tertiary);
            margin-top: 4px;
        }

        /* Modern Navigation */
        .nav-list {
            list-style: none;
            padding: var(--space-4);
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .nav-section {
            margin-bottom: var(--space-6);
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 12px;
            padding: 0 12px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 14px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
            gap: 12px;
        }

        .nav-item:hover {
            background: var(--surface-elevated);
            color: var(--text-primary);
            transform: translateX(4px);
        }

        .nav-item.active {
            background: black;
            color: white;
            box-shadow: var(--shadow-lg);
            transform: translateX(4px);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: white;
            border-radius: var(--radius-full);
        }

        .nav-icon {
            font-size: 20px;
            min-width: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .nav-badge {
            background: var(--error-gradient);
            color: white;
            font-size: 12px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 50px;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Sidebar Collapse Toggle */
        .sidebar-toggle {
            position: absolute;
            top: 14px;
            right: 15px;
            width: 36px;
            height: 36px;
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            color: #000000;
        }

        .sidebar-toggle:hover {
            background: #f8fafc;
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        .sidebar-toggle .material-icons {
            font-size: 18px;
            color: #000000;
            font-weight: bold;
        }

        /* Dark theme toggle */
        [data-theme="dark"] .sidebar-toggle {
            background: #0a0a0a;
            border-color: #262626;
            color: #ffffff;
        }

        [data-theme="dark"] .sidebar-toggle:hover {
            background: #171717;
        }

        [data-theme="dark"] .sidebar-toggle .material-icons {
            color: #ffffff;
        }

        /* Collapsed Sidebar Styles */
        .sidebar.collapsed {
            width: 70px !important;
            min-width: 70px !important;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .nav-text,
        .sidebar.collapsed .nav-badge,
        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle,
        .sidebar.collapsed .nav-section-title {
            display: none !important;
        }

        .sidebar.collapsed .nav-item {
            justify-content: center !important;
            padding: 12px 0 !important;
            margin: 4px 8px !important;
            width: calc(100% - 16px) !important;
            display: flex !important;
            align-items: center !important;
        }

        .sidebar.collapsed .nav-item .material-icons {
            margin: 0 !important;
            font-size: 20px !important;
            text-align: center !important;
            width: 100% !important;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 14px 4px !important;
            text-align: center !important;
        }

        .sidebar.collapsed .sidebar-toggle {
            right: -18px;
        }

        .sidebar.collapsed .nav-list {
            padding: 14px 0 !important;
        }

        /* Ensure smooth transitions */
        .sidebar {
            transition: all 0.3s ease;
        }

        .main-content {
            transition: all 0.3s ease;
        }

        /* Compact Main Content - Responsive */
        .main-content {
            margin-left: calc(260px + 18px); /* sidebar width + space */
            margin-top: 60px; /* header height */
            padding: 14px;
            flex: 1;
            background: var(--background);
            height: calc(100vh - 60px);
            min-height: calc(100vh - 60px);
            max-height: calc(100vh - 60px);
            width: calc(100vw - 260px - 18px);
            max-width: calc(100vw - 260px - 18px);
            min-width: 320px;
            transition: all 0.3s ease;
            overflow-y: hidden;
            overflow-x: hidden;
            flex-shrink: 0;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .main-content.sidebar-collapsed {
            margin-left: calc(70px + 8px) !important; /* collapsed width + minimal space */
            width: calc(100vw - 70px - 8px) !important;
            max-width: calc(100vw - 70px - 8px) !important;
        }

        /* Compact Page Header */
        .page-header {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }

        .page-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--space-4);
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--text-primary);
            margin-bottom: var(--space-1);
        }

        .page-subtitle {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-normal);
        }

        .page-actions {
            display: flex;
            gap: var(--space-3);
            align-items: center;
            padding: var(--space-4);
            background: var(--surface-elevated);
            border-bottom: 1px solid var(--divider);
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            min-width: 0;
            flex-wrap: nowrap;
            flex-shrink: 0;
        }

        .page-actions > * {
            flex-shrink: 0;
        }

        /* Compact Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-4);
        }

        .stat-card {
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            position: relative;
            overflow: hidden;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .stat-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: var(--space-3);
        }

        .stat-value {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--text-primary);
            margin-bottom: var(--space-1);
        }

        .stat-label {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            font-weight: var(--font-medium);
        }

        .stat-change {
            font-size: var(--text-xs);
            font-weight: var(--font-semibold);
            margin-top: var(--space-2);
        }

        .stat-change.positive {
            color: var(--success-color);
        }

        .stat-change.negative {
            color: var(--error-color);
        }

        /* Modern Button System */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            font-family: var(--font-family-primary);
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            text-decoration: none;
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        /* Primary Button */
        .btn-primary {
            background: black;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        /* Secondary Button */
        .btn-secondary {
            background: var(--surface);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        .btn-secondary:hover:not(:disabled) {
            background: black;
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Danger Button */
        .btn-danger {
            background: var(--error-gradient);
            color: white;
            border: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-danger:hover:not(:disabled) {
            background: var(--error-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        /* Outline Button */
        .btn-outline {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-outline:hover:not(:disabled) {
            background: var(--surface-elevated);
            border-color: black;
            color: black;
        }

        /* Ghost Button */
        .btn-ghost {
            background: transparent;
            color: var(--text-secondary);
            border: none;
        }

        .btn-ghost:hover:not(:disabled) {
            background: var(--surface-elevated);
            color: var(--text-primary);
        }

        /* Icon Button */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            border-radius: var(--radius-full);
            background: var(--surface);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            box-shadow: var(--shadow-sm);
        }

        .btn-icon:hover:not(:disabled) {
            background: var(--surface-elevated);
            color: var(--text-primary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Button Sizes */
        .btn-sm {
            padding: var(--space-2) var(--space-4);
            font-size: var(--text-xs);
        }

        .btn-lg {
            padding: var(--space-4) var(--space-8);
            font-size: var(--text-base);
        }

        .btn-icon-sm {
            width: 32px;
            height: 32px;
        }

        .btn-icon-lg {
            width: 48px;
            height: 48px;
        }

        .btn-badge {
            background: var(--error-color);
            color: white;
            border-radius: var(--radius-full);
            padding: 2px 6px;
            font-size: 10px;
            font-weight: var(--font-bold);
            margin-left: var(--space-2);
            min-width: 18px;
            text-align: center;
        }

        /* Theme-specific Button Enhancements */
        [data-theme="dark"] .btn-primary {
            box-shadow: var(--shadow-lg), 0 0 20px rgba(99, 102, 241, 0.3);
        }

        [data-theme="dark"] .btn-primary:hover:not(:disabled) {
            box-shadow: var(--shadow-xl), 0 0 30px rgba(99, 102, 241, 0.4);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--surface-elevated);
            border-color: white;
            box-shadow: var(--shadow-md);
        }

        [data-theme="dark"] .btn-outline {
            background: var(--surface);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .btn-outline:hover:not(:disabled) {
            background: var(--surface-elevated);
            border-color: white;
            color: white;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
        }

        [data-theme="dark"] .btn-icon {
            background: var(--surface-elevated);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .btn-icon:hover:not(:disabled) {
            background: var(--surface);
            border-color: white;
            color: white;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        /* Compact Data Container - Responsive */
        .data-container {
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            margin-top: 0px;
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            min-width: 320px;
            width: 100%;
            max-width: 100%;
            flex-shrink: 0;
            position: relative;
            transition: all 0.3s ease;
        }

        /* Compact Data Header with View Toggle */
        .data-header {
            padding: var(--space-4);
            border-bottom: 1px solid var(--divider);
            background: var(--surface-elevated);
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--space-4);
            flex-shrink: 0;
        }

        .data-title {
            font-size: var(--text-xl);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
        }

        .data-controls {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        /* View Toggle */
        .view-toggle {
            display: flex;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--space-1);
        }

        .view-toggle-btn {
            padding: var(--space-2) var(--space-4);
            border: none;
            background: transparent;
            color: var(--text-secondary);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
        }

        .view-toggle-btn.active {
            background: black;
            color: white;
            box-shadow: var(--shadow-sm);
        }

        /* Modern Filter Bar */
        .filter-bar {
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--divider);
            display: flex;
            gap: var(--space-4);
            align-items: center;
            background: var(--background);
            flex-wrap: wrap;
            min-width: 0;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .filter-chip {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-3);
            background: black;
            color: white;
            border-radius: var(--radius-full);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
        }

        .filter-chip-remove {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0;
            width: 16px;
            height: 16px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .filter-chip-remove:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Hybrid Table-Card View */
        .data-view {
            position: relative;
            min-height: 400px;
        }

        /* Table View */
        .table-view {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            min-width: 100%;
        }

        .modern-table {
            width: 100%;
            min-width: 800px; /* Ensure minimum width for all columns */
            border-collapse: separate;
            border-spacing: 0;
        }

        .modern-table thead {
            background: var(--surface-elevated);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-table th {
            padding: var(--space-4) var(--space-6);
            text-align: left;
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 1px solid var(--divider);
            white-space: nowrap;
        }

        .modern-table th:first-child {
            border-top-left-radius: var(--radius-lg);
        }

        .modern-table th:last-child {
            border-top-right-radius: var(--radius-lg);
        }

        .modern-table tbody tr {
            transition: all var(--transition-fast);
            border-bottom: 1px solid var(--border-light);
        }

        .modern-table tbody tr:hover {
            background: var(--surface-elevated);
        }

        .modern-table tbody tr:last-child {
            border-bottom: none;
        }

        .modern-table td {
            padding: var(--space-4) var(--space-6);
            font-size: var(--text-sm);
            color: var(--text-primary);
            vertical-align: middle;
        }

        /* Data View Container - Always Scrollable */
        .data-view {
            flex: 1;
            overflow-x: hidden;
            overflow-y: auto;
            min-height: 0;
            height: 100%;
            min-width: 320px;
            -webkit-overflow-scrolling: touch;
            position: relative;
            flex-shrink: 0;
        }

        /* Responsive Card View with Both Scrolling Directions */
        .card-view {
            display: block;
            height: 100%;
            min-height: 0;
            overflow-y: auto;
            overflow-x: hidden;
            width: 100%;
            padding: var(--space-4);
            -webkit-overflow-scrolling: touch;
        }

        /* Cards Grid - Responsive Layout */
        .cards-scroll-container {
            width: 100%;
            height: auto;
            min-height: 100%;
        }

        .cards-horizontal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 14px;
            width: 100%;
            height: auto;
            padding: 14px;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        /* Responsive grid adjustments for sidebar states */
        .main-content .cards-horizontal-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        .main-content.sidebar-collapsed .cards-horizontal-grid {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        }

        /* Ultra-wide screens */
        @media (min-width: 1800px) {
            .cards-horizontal-grid {
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: var(--space-6);
            }
        }

        /* Large desktop */
        @media (min-width: 1400px) and (max-width: 1799px) {
            .cards-horizontal-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: var(--space-5);
            }
        }

        /* Medium desktop */
        @media (min-width: 1024px) and (max-width: 1399px) {
            .cards-horizontal-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: var(--space-4);
            }
        }

        /* Tablet */
        @media (min-width: 768px) and (max-width: 1023px) {
            .cards-horizontal-grid {
                grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
                gap: var(--space-4);
                padding: var(--space-3);
            }
        }

        /* Mobile */
        @media (max-width: 767px) {
            .cards-horizontal-grid {
                grid-template-columns: 1fr;
                gap: var(--space-3);
                padding: var(--space-3);
            }
        }

        /* Small mobile */
        @media (max-width: 480px) {
            .cards-horizontal-grid {
                grid-template-columns: 1fr;
                gap: var(--space-2);
                padding: var(--space-2);
            }
        }

        .kit-card {
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 14px;
            transition: all 0.2s ease;
            position: relative;
            overflow: visible;
            cursor: pointer;
            width: 100%;
            max-width: 100%;
            min-width: 0;
            height: auto;
            min-height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            box-sizing: border-box;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Responsive card sizing */
        @media (min-width: 1400px) {
            .kit-card {
                min-height: 200px;
                padding: var(--space-5);
            }
        }

        @media (max-width: 767px) {
            .kit-card {
                min-height: 160px;
                padding: var(--space-3);
            }
        }

        @media (max-width: 480px) {
            .kit-card {
                min-height: 140px;
                padding: var(--space-3);
            }
        }

        .kit-card.selected {
            border-color: black;
            background: var(--surface-elevated);
            box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
        }

        .kit-card-checkbox {
            position: absolute;
            top: var(--space-3);
            left: var(--space-3);
            width: 24px;
            height: 24px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-sm);
            background: var(--surface);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all var(--transition-fast);
            z-index: 10;
            box-shadow: var(--shadow-sm);
        }

        .kit-card-edit {
            position: absolute;
            top: calc(var(--space-3) + 32px);
            left: var(--space-3);
            width: 24px;
            height: 24px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-sm);
            background: var(--surface);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all var(--transition-fast);
            z-index: 10;
            box-shadow: var(--shadow-sm);
            color: black;
        }

        .kit-card:hover .kit-card-checkbox,
        .kit-card.selected .kit-card-checkbox,
        .kit-card:hover .kit-card-edit {
            opacity: 1;
        }

        .kit-card.selected .kit-card-checkbox {
            background: black;
            border-color: black;
            color: white;
        }

        .kit-card-edit:hover {
            background: black;
            border-color: black;
            color: white;
            transform: scale(1.1);
        }

        .kit-card:hover {
            box-shadow: var(--shadow-xl);
        }

        .kit-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-2);
            margin-left: 36px; /* Space for checkbox and edit button */
            flex-shrink: 0;
        }

        .kit-part-number {
            display: flex;
            flex-direction: column;
            gap: 2px;
            font-family: var(--font-family-primary);
            text-align: left;
            line-height: 1.2;
        }

        .kit-description {
            display: flex;
            flex-direction: column;
            gap: 2px;
            margin-bottom: var(--space-2);
            line-height: 1.3;
            flex: 1;
            margin-left: 36px; /* Align with header */
            word-wrap: break-word;
            overflow-wrap: break-word;
            text-align: left;
        }

        .kit-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--space-2);
            margin-bottom: var(--space-2);
            flex-shrink: 0;
            margin-left: 36px; /* Align with header */
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .detail-label {
            font-size: 14px;
            color: var(--text-tertiary);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            text-align: left;
            margin-bottom: 2px;
        }

        .detail-value {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
            text-align: left;
            font-weight: 800;
        }



        /* Modern Status Badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid transparent;
        }

        .status-active {
            background: var(--success-gradient);
            color: white;
        }

        .status-inactive {
            background: var(--error-gradient);
            color: white;
        }

        .status-pending {
            background: var(--warning-gradient);
            color: white;
        }

        /* Theme-specific Status Badge Enhancements */
        [data-theme="dark"] .status-badge {
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Theme-specific Card Enhancements */
        [data-theme="dark"] .kit-card {
            border: 1px solid var(--border-color);
            background: linear-gradient(145deg, var(--surface) 0%, var(--surface-elevated) 100%);
        }

        [data-theme="dark"] .kit-card:hover {
            box-shadow: var(--shadow-xl);
        }

        /* Theme-specific Table Enhancements */
        [data-theme="dark"] .modern-table {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        [data-theme="dark"] .modern-table thead {
            background: linear-gradient(145deg, var(--surface-elevated) 0%, var(--surface) 100%);
        }

        [data-theme="dark"] .modern-table tbody tr:hover {
            background: var(--surface-elevated);
            box-shadow: inset 0 0 0 1px white;
        }

        /* Modern Modal System */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: var(--z-modal-backdrop);
            opacity: 0;
            transition: all var(--transition-normal);
        }

        .modal-backdrop.active {
            display: flex;
            opacity: 1;
        }

        .modal-dialog {
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-2xl);
            width: 90%;
            max-width: 700px;
            min-width: 320px;
            max-height: 85vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: all var(--transition-normal);
            display: flex;
            flex-direction: column;
        }

        .modal-backdrop.active .modal-dialog {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--divider);
            background: var(--surface-elevated);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .modal-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--text-primary);
        }

        .modal-close {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: var(--surface);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--text-secondary);
            transition: all var(--transition-fast);
        }

        .modal-close:hover {
            background: var(--surface-elevated);
            color: var(--text-primary);
            transform: scale(1.1);
        }

        .modal-body {
            padding: var(--space-6);
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }

        /* Modal Table Fixes */
        .modal-body .table-view {
            overflow-x: hidden;
        }

        .modal-body .modern-table {
            width: 100%;
            min-width: auto;
            table-layout: fixed;
        }

        .modal-body .modern-table th,
        .modal-body .modern-table td {
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
            max-width: 0;
        }

        .modal-body .modern-table th:nth-child(1),
        .modal-body .modern-table td:nth-child(1) {
            width: 15%;
        }

        .modal-body .modern-table th:nth-child(2),
        .modal-body .modern-table td:nth-child(2) {
            width: 25%;
        }

        .modal-body .modern-table th:nth-child(3),
        .modal-body .modern-table td:nth-child(3) {
            width: 45%;
        }

        .modal-body .modern-table th:nth-child(4),
        .modal-body .modern-table td:nth-child(4) {
            width: 15%;
        }

        .modal-footer {
            padding: var(--space-6);
            border-top: 1px solid var(--divider);
            background: var(--surface-elevated);
            display: flex;
            justify-content: flex-end;
            gap: var(--space-3);
            flex-wrap: wrap;
            flex-shrink: 0;
        }

        /* Modern Form System */
        .form-group {
            margin-bottom: var(--space-6);
        }

        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: var(--space-4);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--surface);
            color: var(--text-primary);
            font-size: var(--text-sm);
            font-family: var(--font-family-primary);
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: black;
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder,
        .form-textarea::placeholder {
            color: var(--text-tertiary);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
            line-height: var(--leading-relaxed);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-4);
        }

        .form-section {
            margin-bottom: var(--space-8);
        }

        .form-section-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            padding-bottom: var(--space-2);
            border-bottom: 2px solid var(--border-light);
        }

        /* Theme-specific Form Enhancements */
        [data-theme="dark"] .form-input,
        [data-theme="dark"] .form-select,
        [data-theme="dark"] .form-textarea {
            background: var(--surface-elevated);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .form-input:focus,
        [data-theme="dark"] .form-select:focus,
        [data-theme="dark"] .form-textarea:focus {
            background: var(--surface);
            border-color: white;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2), 0 0 15px rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .form-section-title {
            border-bottom-color: var(--border-color);
        }

        /* Search Input Container */
        .search-input-container {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
        }

        .search-input-container .form-input {
            flex: 1;
            padding-right: 45px;
        }

        .search-icon-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 6px;
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            z-index: 10;
        }

        .search-icon-btn:hover:not(:disabled) {
            background: var(--surface-elevated);
            color: black;
            transform: translateY(-50%) scale(1.1);
        }

        .search-icon-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .search-icon-btn .material-icons {
            font-size: 18px;
        }

        /* Dark theme search icon */
        [data-theme="dark"] .search-icon-btn:hover:not(:disabled) {
            background: var(--surface);
            color: white;
        }

        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 var(--radius-lg) var(--radius-lg);
            box-shadow: var(--shadow-lg);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-dropdown.show {
            display: block;
        }

        .search-dropdown-item {
            padding: var(--space-3) var(--space-4);
            cursor: pointer;
            border-bottom: 1px solid var(--border-light);
            transition: all var(--transition-fast);
            font-size: var(--text-sm);
        }

        .search-dropdown-item:last-child {
            border-bottom: none;
        }

        .search-dropdown-item:hover {
            background: var(--surface-elevated);
            color: var(--text-primary);
        }

        .search-dropdown-item.selected {
            background: black;
            color: white;
        }

        .search-dropdown-item-main {
            font-weight: var(--font-semibold);
            color: var(--text-primary);
        }

        .search-dropdown-item-sub {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            margin-top: 2px;
        }

        /* Dark theme search dropdown */
        [data-theme="dark"] .search-dropdown {
            background: var(--surface-elevated);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .search-dropdown-item:hover {
            background: var(--surface);
        }

        [data-theme="dark"] .search-dropdown-item.selected {
            background: white;
            color: black;
        }

        /* Compact Table Styles */
        .compact-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            background: var(--surface);
        }

        .compact-table th {
            background: var(--surface-elevated);
            color: var(--text-primary);
            font-weight: var(--font-semibold);
            padding: 8px 6px;
            text-align: left;
            border-bottom: 2px solid var(--border-color);
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .compact-table td {
            padding: 6px 6px;
            border-bottom: 1px solid var(--border-light);
            vertical-align: middle;
            font-size: 12px;
        }

        .compact-table tbody tr:hover {
            background: var(--surface-elevated);
        }

        .compact-table input[type="number"] {
            width: 100%;
            padding: 2px 4px;
            border: 1px solid var(--border-color);
            border-radius: 3px;
            font-size: 11px;
            text-align: center;
        }

        .compact-table .btn-icon-sm {
            width: 24px;
            height: 24px;
            padding: 2px;
            border-radius: 3px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
        }

        .compact-table .btn-icon-sm:hover {
            background: var(--error-light);
            color: var(--error);
        }

        .compact-table .btn-icon-sm .material-icons {
            font-size: 16px;
        }

        /* Dark theme compact table */
        [data-theme="dark"] .compact-table {
            background: var(--surface);
        }

        [data-theme="dark"] .compact-table th {
            background: var(--surface-elevated);
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .compact-table td {
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .compact-table tbody tr:hover {
            background: var(--surface-elevated);
        }

        [data-theme="dark"] .compact-table input[type="number"] {
            background: var(--surface);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        /* Kit BOM Layout Styles */
        .modal-dialog-wide {
            max-width: 1200px;
            width: 95%;
        }

        .kit-bom-layout {
            display: flex;
            gap: 24px;
            min-height: 500px;
        }

        .kit-form-section {
            flex: 1;
            min-width: 400px;
        }

        .kit-parts-section {
            flex: 1;
            min-width: 400px;
            background: var(--surface-elevated);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .kit-parts-section h4 {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .kit-parts-section h4::before {
            content: "📋";
            font-size: 16px;
        }

        /* Responsive layout for smaller screens */
        @media (max-width: 1024px) {
            .modal-dialog-wide {
                max-width: 95%;
                width: 95%;
            }

            .kit-bom-layout {
                flex-direction: column;
                gap: 16px;
            }

            .kit-form-section,
            .kit-parts-section {
                min-width: auto;
                flex: none;
            }

            .kit-parts-section {
                max-height: 300px;
            }
        }

        /* Dark theme for kit BOM layout */
        [data-theme="dark"] .kit-parts-section {
            background: var(--surface);
            border-color: var(--border-color);
        }

        /* Enhanced scrollbar styling for parts table - vertical scroll */
        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: var(--surface-elevated);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: var(--text-tertiary);
        }

        /* Table styling for proper fit */
        .compact-table {
            border-collapse: collapse;
        }

        .compact-table thead th {
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            padding: 12px 8px;
        }

        /* Better table cell alignment */
        .compact-table td {
            vertical-align: middle;
            padding: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Description column can wrap and break words */
        .compact-table td:nth-child(3) {
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            max-width: 0;
        }

        /* Other columns should not wrap */
        .compact-table td:not(:nth-child(3)) {
            white-space: nowrap;
        }

        /* Pagination Styles */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-4);
            background: var(--surface-elevated);
            border-top: 1px solid var(--divider);
            flex-shrink: 0;
            gap: var(--space-4);
        }

        .pagination-info {
            display: flex;
            align-items: center;
            gap: var(--space-6);
            font-size: 14px;
            color: var(--text-secondary);
        }

        .items-per-page {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .items-per-page label {
            font-size: 14px;
            color: var(--text-secondary);
            white-space: nowrap;
        }

        .form-select-sm {
            padding: 4px 8px;
            font-size: 14px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            background: var(--surface);
            color: var(--text-primary);
            min-width: 60px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .pagination-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border: 1px solid var(--border-color);
            background: var(--surface);
            color: var(--text-primary);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: 14px;
        }

        .pagination-btn:hover:not(:disabled) {
            background: var(--surface-elevated);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: var(--surface-elevated);
        }

        .pagination-btn .material-icons {
            font-size: 18px;
        }

        .pagination-numbers {
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .pagination-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border: 1px solid var(--border-color);
            background: var(--surface);
            color: var(--text-primary);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: 14px;
            font-weight: var(--font-medium);
        }

        .pagination-number:hover {
            background: var(--surface-elevated);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .pagination-number.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .pagination-ellipsis {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            color: var(--text-tertiary);
            font-size: 14px;
        }

        /* Responsive Pagination */
        @media (max-width: 768px) {
            .pagination-container {
                flex-direction: column;
                gap: var(--space-3);
                align-items: stretch;
            }

            .pagination-info {
                justify-content: center;
                flex-wrap: wrap;
                gap: var(--space-3);
            }

            .pagination-controls {
                justify-content: center;
                flex-wrap: wrap;
            }

            .pagination-numbers {
                order: -1;
                justify-content: center;
                flex-wrap: wrap;
            }
        }

        @media (max-width: 480px) {
            .pagination-btn,
            .pagination-number {
                width: 32px;
                height: 32px;
                font-size: 12px;
            }

            .pagination-btn .material-icons {
                font-size: 16px;
            }

            .items-per-page {
                flex-direction: column;
                gap: var(--space-1);
                text-align: center;
            }
        }

        /* Detail Display Elements */
        .detail-display {
            padding: var(--space-3);
            background: var(--surface-elevated);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-family: var(--font-family-mono);
            font-weight: var(--font-medium);
            color: var(--text-primary);
            min-height: 20px;
        }

        /* Modern Loading States */
        .loading-skeleton {
            background: linear-gradient(90deg, var(--border-light) 25%, var(--border-color) 50%, var(--border-light) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .skeleton-card {
            height: 200px;
            border-radius: var(--radius-xl);
        }

        .skeleton-text {
            height: 16px;
            border-radius: var(--radius-sm);
            margin-bottom: var(--space-2);
        }

        .skeleton-text.short {
            width: 60%;
        }

        /* Modern Empty States */
        .empty-state {
            text-align: center;
            padding: var(--space-16) var(--space-8);
            color: var(--text-tertiary);
        }

        .empty-state-icon {
            font-size: 64px;
            margin-bottom: var(--space-4);
            opacity: 0.5;
        }

        .empty-state-title {
            font-size: var(--text-xl);
            font-weight: var(--font-semibold);
            color: var(--text-secondary);
            margin-bottom: var(--space-2);
        }

        .empty-state-description {
            font-size: var(--text-sm);
            color: var(--text-tertiary);
            margin-bottom: var(--space-6);
        }

        /* Modern Responsive Design */
        @media (min-width: 1025px) and (max-width: 1200px) {
            .main-content {
                margin-left: calc(70px + 8px) !important;
                width: calc(100vw - 70px - 8px) !important;
                max-width: calc(100vw - 70px - 8px) !important;
                height: calc(100vh - 60px) !important;
                min-height: calc(100vh - 60px) !important;
                max-height: calc(100vh - 60px) !important;
                overflow-y: hidden !important;
                overflow-x: hidden !important;
            }

            .sidebar {
                width: 70px !important;
                min-width: 70px !important;
            }

            .sidebar .nav-text,
            .sidebar .nav-badge,
            .sidebar .sidebar-title,
            .sidebar .sidebar-subtitle,
            .sidebar .nav-section-title {
                display: none !important;
            }

            .sidebar .nav-item {
                justify-content: center !important;
                padding: 12px 0 !important;
                margin: 4px 8px !important;
                width: calc(100% - 16px) !important;
            }

            .sidebar .nav-item .material-icons {
                margin: 0 !important;
                font-size: 20px !important;
                text-align: center !important;
                width: 100% !important;
            }

            /* Responsive cards for medium screens */
            .cards-horizontal-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
                gap: 12px !important;
            }
        }

        /* Large desktop - full sidebar */
        @media (min-width: 1201px) {
            .main-content {
                margin-left: calc(260px + 12px) !important;
                width: calc(100vw - 260px - 12px) !important;
                max-width: calc(100vw - 260px - 12px) !important;
                height: calc(100vh - 60px) !important;
                min-height: calc(100vh - 60px) !important;
                max-height: calc(100vh - 60px) !important;
                overflow-y: hidden !important;
                overflow-x: hidden !important;
            }

            .sidebar {
                width: 260px !important;
                min-width: 260px !important;
                top:74px;
            }

            .sidebar .nav-text,
            .sidebar .nav-badge,
            .sidebar .sidebar-title,
            .sidebar .sidebar-subtitle,
            .sidebar .nav-section-title {
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
                width: auto !important;
            }

            /* Responsive cards for large screens */
            .cards-horizontal-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
                gap: 14px !important;
            }
        }

        /* Tablet Responsive Design */
        @media (min-width: 769px) and (max-width: 1024px) {
            .main-content {
                margin-left: calc(70px + 14px) !important;
                width: calc(100vw - 70px - 14px) !important;
                max-width: calc(100vw - 70px - 14px) !important;
                padding: 12px !important;
                height: calc(100vh - 60px) !important;
                min-height: calc(100vh - 60px) !important;
                max-height: calc(100vh - 60px) !important;
                overflow-y: hidden !important;
                overflow-x: hidden !important;
            }

            .sidebar {
                width: 70px !important;
                min-width: 70px !important;
            }

            .sidebar .nav-text,
            .sidebar .nav-badge,
            .sidebar .sidebar-title,
            .sidebar .sidebar-subtitle,
            .sidebar .nav-section-title {
                display: none !important;
            }

            .sidebar .nav-item {
                justify-content: center !important;
                padding: 12px 0 !important;
                margin: 4px 8px !important;
                width: calc(100% - 16px) !important;
            }

            .sidebar .nav-item .material-icons {
                margin: 0 !important;
                font-size: 20px !important;
                text-align: center !important;
                width: 100% !important;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-row {
                grid-template-columns: 1fr 1fr;
            }

            .data-header {
                flex-direction: row;
                flex-wrap: nowrap;
                gap: var(--space-4);
                overflow-x: auto;
            }

            .search-container {
                max-width: 300px;
            }
        }

        /* Enhanced Mobile Responsiveness */
        @media (max-width: 768px) {
            /* Mobile Header */
            .app-header {
                padding: var(--space-3) var(--space-4);
                height: 56px;
                flex-wrap: wrap;
            }

            .header-left {
                gap: var(--space-3);
                flex: none;
            }

            .mobile-menu-toggle {
                display: flex !important;
                align-items: center;
                justify-content: center;
            }

            .logo {
                font-size: 18px;
            }

            .breadcrumbs {
                display: none;
            }

            .search-container {
                flex: 1;
                max-width: none;
                margin: 0 var(--space-3);
                min-width: 120px;
            }

            .search-input {
                font-size: 16px; /* Prevents zoom on iOS */
                padding: var(--space-2) var(--space-3);
            }

            .header-actions {
                gap: var(--space-2);
            }

            /* Mobile Sidebar */
            .sidebar {
                left: -100%;
                width: 280px;
                top: 56px;
                height: calc(100vh - 56px);
                transition: left var(--transition-normal);
                z-index: var(--z-modal);
                border-radius: 0;
                border-left: none;
            }

            .sidebar.open {
                left: 0;
            }

            .sidebar.mobile-open {
                left: 0;
            }

            /* Mobile overlay for sidebar */
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: calc(var(--z-modal) - 1);
                opacity: 0;
                visibility: hidden;
                transition: all var(--transition-normal);
            }

            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }

            /* Mobile Main Content */
            .main-content {
                margin-left: 0;
                margin-top: 56px;
                padding: var(--space-3);
                height: calc(100vh - 56px);
                min-height: 500px;
                width: 100vw;
                max-width: 100vw;
                overflow-x: hidden;
                overflow-y: auto;
                flex-shrink: 0;

            }

            .data-container {
                height: calc(100vh - 200px);
                min-height: 400px;
                width: 100%;
                max-width: 100%;
                flex-shrink: 0;
            }

            /* Mobile Page Header */
            .page-header-top {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-3);
            }

            .page-actions {
                justify-content: flex-start;
                flex-wrap: wrap;
                gap: var(--space-2);
            }

            /* Mobile Stats */
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-3);
            }

            /* Mobile Cards - Horizontal Scroll */
            .cards-scroll-container {
                padding: var(--space-3);
            }

            .cards-horizontal-grid {
                gap: var(--space-3);
            }

            .kit-card {
                flex: 0 0 300px; /* Consistent width for horizontal scroll */
                padding: var(--space-3);
                min-width: 300px;
                max-width: 300px;
                width: 300px;
                min-height: 280px;
                height: auto;
            }

            .kit-card-header {
                margin-left: 32px; /* Adjust for smaller checkbox and edit button */
            }

            .kit-card-checkbox {
                width: 20px;
                height: 20px;
                top: var(--space-2);
                left: var(--space-2);
            }

            .kit-card-edit {
                width: 20px;
                height: 20px;
                top: calc(var(--space-2) + 28px);
                left: var(--space-2);
            }

            /* Mobile Forms */
            .form-row {
                grid-template-columns: 1fr;
                gap: var(--space-3);
            }

            /* Mobile Data Header */
            .data-header {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-3);
                padding: var(--space-3);
            }

            .data-controls {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-3);
            }

            .view-toggle {
                align-self: flex-start;
            }

            /* Mobile Filter Bar */
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-3);
                padding: var(--space-3);
            }

            .filter-group {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-2);
            }

            /* Mobile Table Wrapper */
            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .modern-table {
                min-width: 600px;
            }

            /* Mobile Buttons */
            .btn {
                min-height: 44px; /* Touch-friendly */
                padding: var(--space-3) var(--space-4);
                font-size: var(--text-sm);
            }

            .btn-icon {
                min-width: 44px;
                min-height: 44px;
            }

            /* Mobile Modals */
            .modal-dialog {
                width: 95%;
                max-width: none;
                margin: var(--space-4);
                max-height: calc(100vh - 2rem);
            }

            .modal-header {
                padding: var(--space-4);
                flex-wrap: nowrap;
                overflow-x: auto;
            }

            .modal-body {
                padding: var(--space-4);
                max-height: calc(70vh - 120px);
            }

            .modal-footer {
                padding: var(--space-4);
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-2);
            }

            .modal-footer .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Small Mobile Devices */
        @media (max-width: 480px) {
            /* Extra small mobile header */
            .app-header {
                padding: var(--space-2) var(--space-3);
            }

            .header-left .logo {
                display: none; /* Hide logo on very small screens */
            }

            .search-container {
                margin: 0 var(--space-2);
            }

            /* Single column stats */
            .stats-grid {
                grid-template-columns: 1fr;
            }

            /* Compact page title */
            .page-title {
                font-size: var(--text-2xl);
            }

            /* Full-width modal */
            .modal-dialog {
                width: 95%;
                margin: var(--space-2);
                max-height: 95vh;
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: var(--space-4);
            }

            /* Compact buttons */
            .btn {
                padding: var(--space-2) var(--space-3);
                font-size: var(--text-xs);
                min-height: 40px;
            }

            /* Smaller cards for small mobile */
            .kit-card {
                flex: 0 0 260px; /* Smaller width for small screens */
                min-width: 260px;
                padding: var(--space-2);
                min-height: 260px;
                height: auto;
            }

            .kit-card-header {
                margin-left: 28px;
            }

            .kit-card-checkbox {
                width: 18px;
                height: 18px;
            }

            .kit-card-edit {
                width: 18px;
                height: 18px;
                top: calc(var(--space-1) + 26px);
                left: var(--space-1);
            }

            /* Compact details grid */
            .kit-details {
                grid-template-columns: 1fr;
                gap: var(--space-1);
            }

            /* Smaller form inputs */
            .form-input,
            .form-select,
            .form-textarea {
                padding: var(--space-3);
                font-size: var(--text-sm);
            }

            /* Compact table */
            .modern-table th,
            .modern-table td {
                padding: var(--space-2) var(--space-3);
                font-size: var(--text-xs);
            }
        }

        /* Modern Animations and Micro-interactions */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        /* Apply animations */
        .kit-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .kit-card:nth-child(even) {
            animation-delay: 0.1s;
        }

        .kit-card:nth-child(3n) {
            animation-delay: 0.2s;
        }

        .stat-card {
            animation: fadeInScale 0.5s ease-out;
        }

        .stat-card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .stat-card:nth-child(3) {
            animation-delay: 0.2s;
        }

        .stat-card:nth-child(4) {
            animation-delay: 0.3s;
        }

        .nav-item {
            animation: slideInRight 0.4s ease-out;
        }

        .nav-item:nth-child(2) {
            animation-delay: 0.05s;
        }

        .nav-item:nth-child(3) {
            animation-delay: 0.1s;
        }

        .nav-item:nth-child(4) {
            animation-delay: 0.15s;
        }

        /* Hover effects */
        .btn:hover {
            animation: pulse 0.6s ease-in-out;
        }

        .notification-bell:hover {
            animation: pulse 0.4s ease-in-out;
        }

        /* Loading shimmer effect */
        .loading-shimmer {
            background: linear-gradient(
                90deg,
                var(--border-light) 0px,
                var(--border-color) 40px,
                var(--border-light) 80px
            );
            background-size: 200px;
            animation: shimmer 1.5s infinite;
        }

        /* Page transition effects */
        .page-enter {
            animation: fadeInUp 0.8s ease-out;
        }

        .modal-enter {
            animation: fadeInScale 0.3s ease-out;
        }

        /* Smooth focus transitions */
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            animation: fadeInScale 0.2s ease-out;
        }

        /* Status badge animations */
        .status-badge {
            animation: fadeInScale 0.4s ease-out;
        }

        /* Modern scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Reduced motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        }

        /* Theme-specific Header Enhancements */
        [data-theme="dark"] .app-header {
            background: var(--glass-bg);
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .search-input {
            background: var(--surface-elevated);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .search-input:focus {
            background: var(--surface);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        [data-theme="dark"] .notification-bell,
        [data-theme="dark"] .theme-toggle,
        [data-theme="dark"] .user-profile {
            background: var(--surface-elevated);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .notification-bell:hover,
        [data-theme="dark"] .theme-toggle:hover,
        [data-theme="dark"] .user-profile:hover {
            background: var(--surface);
            border-color: var(--primary-color);
            box-shadow: 0 0 15px rgba(99, 102, 241, 0.2);
        }

        /* Theme-specific Sidebar Enhancements */
        [data-theme="dark"] .sidebar {
            background: var(--glass-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .sidebar-header {
            background: var(--surface-elevated);
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .nav-item:hover {
            background: var(--surface-elevated);
        }

        [data-theme="dark"] .nav-item.active {
            background: var(--primary-gradient);
            box-shadow: var(--shadow-lg), 0 0 20px rgba(99, 102, 241, 0.3);
        }

        /* Theme-specific Stats Card Enhancements */
        [data-theme="dark"] .stat-card {
            background: linear-gradient(145deg, var(--surface) 0%, var(--surface-elevated) 100%);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .stat-card:hover {
            box-shadow: var(--shadow-xl), 0 0 20px rgba(99, 102, 241, 0.1);
        }

        /* Theme-specific Data Container Enhancements */
        [data-theme="dark"] .data-container {
            background: var(--surface);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .data-header {
            background: var(--surface-elevated);
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .filter-bar {
            background: var(--surface);
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .view-toggle {
            background: var(--surface-elevated);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .view-toggle-btn.active {
            background: var(--primary-gradient);
            box-shadow: var(--shadow-sm);
        }

        /* Theme-specific Modal Enhancements */
        [data-theme="dark"] .modal-backdrop {
            background: rgba(0, 0, 0, 0.8);
        }

        [data-theme="dark"] .modal-dialog {
            background: var(--surface);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .modal-header {
            background: var(--surface-elevated);
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .modal-footer {
            background: var(--surface-elevated);
            border-top-color: var(--border-color);
        }

        [data-theme="dark"] .modal-close {
            background: var(--surface);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .modal-close:hover {
            background: var(--surface-elevated);
            border-color: var(--primary-color);
        }

        /* Theme-specific Page Actions Enhancements */
        [data-theme="dark"] .page-actions {
            background: var(--surface-elevated);
            border-color: var(--border-color);
        }

        /* Loading and Empty States */
        .loading-state,
        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--on-surface);
            opacity: 0.7;
        }

        .loading-state .material-icons,
        .empty-state .material-icons {
            font-size: 48px;
            margin-bottom: var(--spacing-md);
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: calc(var(--header-height) + var(--spacing-md));
            right: var(--spacing-md);
            background-color: var(--surface);
            color: var(--on-surface);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            box-shadow: var(--elevation-2);
            z-index: 3000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid #4caf50;
        }

        .notification.error {
            border-left: 4px solid var(--error);
        }

        .notification.warning {
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Modern Header -->
        <header class="app-header">
            <div class="header-left">
                <button class="btn-icon mobile-menu-toggle" id="mobileMenuToggle">
                    <span class="material-icons">menu</span>
                </button>
                <div class="logo">HCLSoftware</div>
                <div class="breadcrumbs">
                    <div class="breadcrumb-item">
                        <span class="material-icons">home</span>
                        <span>Dashboard</span>
                    </div>
                    <span class="breadcrumb-separator material-icons">chevron_right</span>
                    <div class="breadcrumb-item breadcrumb-current">
                        <span>Kit BOM</span>
                    </div>
                </div>
            </div>
            <div class="search-container">
                <span class="search-icon material-icons">search</span>
                <input type="text" class="search-input" placeholder="Search kit parts..." id="globalSearch">
            </div>
            <div class="header-right">
                <div class="notification-center">
                    <div class="notification-bell">
                        <span class="material-icons">notifications</span>
                        <div class="notification-badge">3</div>
                    </div>
                </div>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <span class="material-icons">dark_mode</span>
                </button>
                <div class="user-profile">
                    <div class="user-avatar">A</div>
                    <div class="user-info">
                        <div class="user-name">Admin</div>
                        <div class="user-role">Branch 11</div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Mobile Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeMobileMenu()"></div>

        <!-- Modern Floating Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-toggle" id="sidebarToggle">
                <span class="material-icons">chevron_left</span>
            </div>

            <!-- <div class="sidebar-header">
                <h3 class="sidebar-title">Navigation</h3>
                <p class="sidebar-subtitle">Main Menu</p>
            </div> -->

            <ul class="nav-list">
                <div class="nav-section">
                    <div class="nav-section-title">Core Modules</div>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">dashboard</span>
                        <span class="nav-text">Dashboard</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">help_outline</span>
                        <span class="nav-text">Helpdesk</span>
                        <!-- <span class="nav-badge">5</span> -->
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">inventory</span>
                        <span class="nav-text">Parts</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">build</span>
                        <span class="nav-text">Service</span>
                    </li>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">schedule</span>
                        <span class="nav-text">TAMS</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">event_available</span>
                        <span class="nav-text">Bay Scheduler</span>
                    </li>
                    <li class="nav-item active">
                        <span class="nav-icon material-icons">list_alt</span>
                        <span class="nav-text">Kit BOM</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">assignment</span>
                        <span class="nav-text">Contracts</span>
                    </li>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Operations</div>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">library_books</span>
                        <span class="nav-text">Digital Catalogue</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">restore</span>
                        <span class="nav-text">Reman</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">build_circle</span>
                        <span class="nav-text">Special Tools</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">shopping_cart</span>
                        <span class="nav-text">Orders</span>
                        <!-- <span class="nav-badge">12</span> -->
                    </li>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Business</div>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">room_service</span>
                        <span class="nav-text">Field Service</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">verified</span>
                        <span class="nav-text">Warranty</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">point_of_sale</span>
                        <span class="nav-text">Sales</span>
                    </li>
                    <li class="nav-item">
                        <span class="nav-icon material-icons">settings</span>
                        <span class="nav-text">Settings</span>
                    </li>
                </div>
            </ul>
        </nav>

        <!-- Modern Main Content -->
        <main class="main-content" id="mainContent">
            <!-- Modern Page Header -->
            <!-- <div class="page-header">
                <div class="page-header-top">
                    <div class="page-title-section">
                        <h1 class="page-title">Kit BOM Management</h1>
                        <p class="page-subtitle">Manage and organize your kit bill of materials with advanced tools</p>
                    </div>
                </div>
            </div> -->

            <!-- Modern Stats Grid -->
            <!-- <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <span class="material-icons">inventory_2</span>
                    </div>
                    <div class="stat-value">156</div>
                    <div class="stat-label">Total Kit Parts</div>
                    <div class="stat-change positive">+12 this month</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <span class="material-icons">check_circle</span>
                    </div>
                    <div class="stat-value">142</div>
                    <div class="stat-label">Active Parts</div>
                    <div class="stat-change positive">91% active rate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <span class="material-icons">category</span>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-label">Categories</div>
                    <div class="stat-change">Across all types</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <span class="material-icons">trending_up</span>
                    </div>
                    <div class="stat-value">98%</div>
                    <div class="stat-label">Data Quality</div>
                    <div class="stat-change positive">+2% improvement</div>
                </div>
            </div> -->



            <!-- Modern Data Container -->
            <div class="data-container">
                <div class="data-header">
                    <h2 class="data-title">Kit BOM</h2>
                    <div class="data-controls">
                        <div class="view-toggle">
                            <button class="view-toggle-btn active" data-view="card">
                                <span class="material-icons">view_module</span>
                                Cards
                            </button>
                            <button class="view-toggle-btn" data-view="table">
                                <span class="material-icons">table_rows</span>
                                Table
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Page Actions -->
                <div class="page-actions">
                    <button class="btn btn-primary" id="newKitBtn">
                        <span class="material-icons">add</span>
                        New Kit
                    </button>
                    <button class="btn btn-outline" id="deleteBtn">
                        <span class="material-icons">delete</span>
                        <span id="deleteButtonText">Delete</span>
                        <span id="deleteCount" class="btn-badge" style="display: none;">0</span>
                    </button>
                    <button class="btn btn-outline" id="selectAllBtn">
                        <span class="material-icons">select_all</span>
                        Select All
                    </button>
                    <button class="btn btn-outline" id="advancedSearchBtn">
                        <span class="material-icons">search</span>
                        Advanced Search
                    </button>
                    <button class="btn btn-outline" id="exportBtn">
                        <span class="material-icons">file_download</span>
                        Export
                    </button>
                    <button class="btn-icon" id="refreshBtn" title="Refresh">
                        <span class="material-icons">refresh</span>
                    </button>
                </div>

                <!-- <div class="filter-bar" id="filterBar">
                    <div class="filter-group">
                        <span style="font-size: var(--text-sm); color: var(--text-secondary); font-weight: var(--font-medium);">Active Filters:</span>
                    </div>

                </div> -->
               <!-- Filter chips will be added dynamically -->
                <div class="data-view" id="dataView">
                    <!-- Card View (Default) -->
                    <div class="card-view" id="cardView">
                        <div class="cards-scroll-container">
                            <div class="cards-horizontal-grid" id="cardsContainer">
                                <!-- Cards will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Table View -->
                    <div class="table-view" id="tableView" style="display: none;">
                        <div class="table-container">
                            <table class="modern-table">
                            <thead>
                                <tr>
                                    <th>Kit Part#</th>
                                    <th>Kit Part Description</th>
                                    <th>Prefix</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="tableBody">
                                <!-- Table rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination Controls -->
                <div class="pagination-container" id="paginationContainer">
                    <div class="pagination-info">
                        <span id="paginationInfo">Showing 1-3 of 8 items</span>
                        <div class="items-per-page">
                            <label for="itemsPerPage">Items per page:</label>
                            <select id="itemsPerPage" class="form-select-sm">
                                <option value="3">3</option>
                                <option value="6">6</option>
                                <option value="9">9</option>
                                <option value="12">12</option>
                                <option value="15">15</option>
                                <option value="20">20</option>
                            </select>
                        </div>
                    </div>
                    <div class="pagination-controls" id="paginationControls">
                        <button class="pagination-btn" id="firstPageBtn" title="First Page">
                            <span class="material-icons">first_page</span>
                        </button>
                        <button class="pagination-btn" id="prevPageBtn" title="Previous Page">
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <div class="pagination-numbers" id="paginationNumbers">
                            <!-- Page numbers will be generated dynamically -->
                        </div>
                        <button class="pagination-btn" id="nextPageBtn" title="Next Page">
                            <span class="material-icons">chevron_right</span>
                        </button>
                        <button class="pagination-btn" id="lastPageBtn" title="Last Page">
                            <span class="material-icons">last_page</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>



    <!-- Kit Details Modal -->
    <div class="modal-backdrop" id="kitDetailsModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h2 class="modal-title" id="kitDetailsTitle">Kit Details</h2>
                <button class="modal-close" onclick="closeModal('kitDetailsModal')">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Kit Part#</label>
                            <div class="detail-display" id="detailPartNumber"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Status</label>
                            <div class="" id="detailStatus"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Kit Part Description</label>
                        <div class="detail-display" id="detailDescription"></div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Type</label>
                            <div class="detail-display" id="detailType"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Category</label>
                            <div class="detail-display" id="detailCategory"></div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="form-section-title">Part Details</h3>
                    <div class="table-view">
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th>Prefix</th>
                                    <th>Part #</th>
                                    <th>Description</th>
                                    <th>Qty</th>
                                </tr>
                            </thead>
                            <tbody id="detailPartsTable">
                                <tr>
                                    <td>N</td>
                                    <td>L322C1</td>
                                    <td>BOBINE POUR SOUPAPE CONTROLE</td>
                                    <td>2</td>
                                </tr>
                                <tr>
                                    <td>N</td>
                                    <td>LF3000</td>
                                    <td>FULL FLOW OIL FILTER</td>
                                    <td>2</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal('kitDetailsModal')">Close</button>
                <!-- <button class="btn btn-primary" onclick="editKitFromDetails()">Edit</button> -->
                <button class="btn btn-outline" onclick="deleteKitFromDetails()">Delete</button>
            </div>
        </div>
    </div>

    <!-- New Kit BOM Modal -->
    <div class="modal-backdrop" id="newKitModal">
        <div class="modal-dialog modal-dialog-wide">
            <div class="modal-header">
                <h2 class="modal-title">Add Kit BOM</h2>
                <button class="modal-close" onclick="closeModal('newKitModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="kit-bom-layout">
                    <!-- Left Side: Form -->
                    <div class="kit-form-section">
                        <form id="newKitForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="kitPartNumber">Kit Part #</label>
                            <div class="search-input-container">
                                <input type="text" class="form-input" id="kitPartNumber" name="kitPartNumber" required autocomplete="off">
                                <button type="button" class="search-icon-btn" onclick="showKitPartSearch()" title="Search Kit Parts">
                                    <span class="material-icons">search</span>
                                </button>
                                <div class="search-dropdown" id="kitPartDropdown"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="kitPartDescription">Kit Part Description</label>
                            <input type="text" class="form-input" id="kitPartDescription" name="kitPartDescription" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="isActive">Is Active?</label>
                        <select class="form-select" id="isActive" name="isActive">
                            <option value="true">Yes</option>
                            <option value="false">No</option>
                        </select>
                    </div>

                    <h3 style="margin: 24px 0 16px 0; color: black;">Part Details</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="prefix">Prefix</label>
                            <input type="text" class="form-input" id="prefix" name="prefix" readonly style="background-color: var(--surface-elevated); color: var(--text-secondary);">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="partNumber">Part #</label>
                            <div class="search-input-container">
                                <input type="text" class="form-input" id="partNumber" name="partNumber" autocomplete="off" disabled>
                                <button type="button" class="search-icon-btn" id="partSearchBtn" onclick="showPartSearch()" title="Search Parts" disabled>
                                    <span class="material-icons">search</span>
                                </button>
                                <div class="search-dropdown" id="partNumberDropdown"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="partDescription">Description</label>
                            <input type="text" class="form-input" id="partDescription" name="partDescription" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="quantity">Quantity</label>
                            <input type="number" class="form-input" id="quantity" name="quantity" min="1" value="1">
                        </div>
                    </div>

                            <div class="form-row" style="margin-top: 16px;">
                                <div class="form-group" style="grid-column: 1 / -1;">
                                    <button type="button" class="btn btn-outline" onclick="addPartToKit()" style="width: 100%;">
                                        <span class="material-icons">add</span>
                                        Add Part to Kit
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Right Side: Parts Table -->
                    <div class="kit-parts-section" id="partsTableSection" style="display: none;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h4 style="margin: 0; color: black; font-size: 18px; font-weight: 600;">Added Parts</h4>
                            <div class="search-input-container" style="width: 200px;">
                                <input type="text" class="form-input" id="addedPartsSearch" placeholder="Search parts..." style="padding: 6px 12px; font-size: 14px;">
                                <span class="material-icons" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); color: var(--text-tertiary); font-size: 18px;">search</span>
                            </div>
                        </div>
                        <div class="table-container" style="max-height: 300px; overflow-y: auto; overflow-x: hidden; border: 1px solid var(--border-color); border-radius: 8px; scrollbar-width: thin;">
                            <table class="compact-table" id="addedPartsTable" style="width: 100%; table-layout: fixed;">
                                <thead style="position: sticky; top: 0; background: var(--surface); z-index: 10;">
                                    <tr>
                                        <th style="width: 12%; text-align: center;">Prefix</th>
                                        <th style="width: 20%;">Part #</th>
                                        <th style="width: 50%;">Description</th>
                                        <th style="width: 10%; text-align: center;">Qty</th>
                                        <th style="width: 8%; text-align: center;">Del</th>
                                    </tr>
                                </thead>
                                <tbody id="addedPartsTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="mdc-button btn-outline" onclick="closeModal('newKitModal')" style="color: black;">Cancel</button>
                <button type="button" class="mdc-button btn-primary" id="saveKitBtn" onclick="saveKit()" style="color: white;">Save</button>
                <button type="button" class="mdc-button btn-primary" id="editKitBtn" onclick="enableEditMode()" style="color: white; display: none;">Edit</button>
                <button type="button" class="mdc-button btn-outline" onclick="saveAndExit()" style="color: black;">Exit</button>
            </div>
        </div>
    </div>

    <!-- Advanced Search Modal -->
    <div class="modal-backdrop" id="advancedSearchModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h2 class="modal-title">Advanced Search</h2>
                <button class="modal-close" onclick="closeModal('advancedSearchModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="advancedSearchForm">
                    <div class="form-group">
                        <label class="form-label" for="searchColumn">Select Column:</label>
                        <select class="form-select" id="searchColumn" name="searchColumn">
                            <option value="">--Select--</option>
                            <option value="partNumber">Part Number</option>
                            <option value="description">Description</option>
                            <option value="type">Type</option>
                            <option value="category">Category</option>
                            <option value="status">Status</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="searchOperator">Select Operator:</label>
                        <select class="form-select" id="searchOperator" name="searchOperator">
                            <option value="">--Select--</option>
                            <option value="equals">Equals</option>
                            <option value="contains">Contains</option>
                            <option value="startsWith">Starts With</option>
                            <option value="endsWith">Ends With</option>
                            <option value="notEquals">Not Equals</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="searchValue">Value:</label>
                        <input type="text" class="form-input" id="searchValue" name="searchValue">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="searchCondition">Select Condition:</label>
                        <select class="form-select" id="searchCondition" name="searchCondition">
                            <option value="">--Select--</option>
                            <option value="and">AND</option>
                            <option value="or">OR</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="searchCriteria">Search Criteria:</label>
                        <textarea class="form-textarea" id="searchCriteria" name="searchCriteria" placeholder="Enter additional search criteria..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="mdc-button btn-outline" onclick="addFilter()" style="color: black !important;">Add Filter</button>
                <button type="button" class="mdc-button btn-outline" onclick="removeFilter()" style="color: black !important;">Remove Filter</button>
                <button type="button" class="mdc-button btn-outline" onclick="closeModal('advancedSearchModal')" style="color: black !important;">Cancel</button>
                <button type="button" class="mdc-button btn-primary" onclick="performAdvancedSearch()" style="color: white !important;">Search</button>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal-backdrop" id="exportModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h2 class="modal-title">Export Data</h2>
                <button class="modal-close" onclick="closeModal('exportModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Select Export Format:</label>
                    <div style="display: flex; flex-direction: column; gap: 12px; margin-top: 8px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="exportFormat" value="excel" checked>
                            <span class="material-icons" style="color: var(--primary-color);">table_chart</span>
                            <span>Excel (.xlsx)</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="exportFormat" value="word">
                            <span class="material-icons" style="color: var(--primary-color);">description</span>
                            <span>Word Document (.docx)</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="exportFormat" value="pdf">
                            <span class="material-icons" style="color: var(--primary-color);">picture_as_pdf</span>
                            <span>PDF Document (.pdf)</span>
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Export Options:</label>
                    <div style="display: flex; flex-direction: column; gap: 8px; margin-top: 8px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" id="includeHeaders" checked>
                            <span>Include column headers</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" id="includeActiveOnly">
                            <span>Export active items only</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" id="includeTimestamp" checked>
                            <span>Include export timestamp</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="mdc-button btn-outline" onclick="closeModal('exportModal')" style="color: black !important;">Cancel</button>
                <button type="button" class="mdc-button btn-primary" onclick="performExport()" style="color:white !important;">
                    <span class="material-icons">file_download</span>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Material Components Web JavaScript -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

    <script>
        // Sample Kit Data
        let kitData = [
            { id: 1, partNumber: '191074', description: '63" AND 71/16 EVAPOR LOWER PANEL DI', type: 'P', category: 'BLOWER CASING', isActive: true },
            { id: 2, partNumber: '192200', description: 'BLOWER CASING', type: 'P', category: 'BLOWER CASING', isActive: true },
            { id: 3, partNumber: 'G1011207', description: 'SPACER ARC CMPR MTG', type: 'N', category: 'SPACER', isActive: true },
            { id: 4, partNumber: 'G1005346', description: 'ANCRAGE JSG 01604', type: 'P', category: 'ANCRAGE', isActive: true },
            { id: 5, partNumber: 'G1200853', description: 'HI THR-WATH (NO) INHIBIT CHKG', type: 'N', category: 'INHIBIT', isActive: true },
            { id: 6, partNumber: 'LF590', description: 'OIL FILTER JOHNSTON', type: 'N', category: 'FILTER', isActive: true },
            { id: 7, partNumber: '191074', description: '63" AND 71/16 EVAPOR LOWER PANEL DI', type: 'P', category: 'BLOWER CASING', isActive: true },
            { id: 8, partNumber: '192200', description: 'BLOWER CASING', type: 'P', category: 'BLOWER CASING', isActive: true }

        ];

        // Hardcoded Kit Part Numbers for search
        const kitPartNumbers = [
            { partNumber: 'KIT001', description: 'Engine Maintenance Kit' },
            { partNumber: 'KIT002', description: 'Hydraulic System Kit' },
            { partNumber: 'KIT003', description: 'Electrical Components Kit' },
            { partNumber: 'KIT004', description: 'Cooling System Kit' },
            { partNumber: 'KIT005', description: 'Transmission Service Kit' },
            { partNumber: 'KIT006', description: 'Brake System Kit' },
            { partNumber: 'KIT007', description: 'Air Filter Kit' },
            { partNumber: 'KIT008', description: 'Oil Change Kit' },
            { partNumber: 'KIT009', description: 'Fuel System Kit' },
            { partNumber: 'KIT010', description: 'Suspension Kit' }
        ];

        // Hardcoded Part Numbers for search
        const partNumbers = [
            { partNumber: 'L322C1', description: 'BOBINE POUR SOUPAPE CONTROLE', price: 45.99, prefix: 'N' },
            { partNumber: 'LF3000', description: 'FULL FLOW OIL FILTER', price: 12.50, prefix: 'N' },
            { partNumber: 'AF25550', description: 'AIR FILTER ELEMENT', price: 28.75, prefix: 'P' },
            { partNumber: 'HF6177', description: 'HYDRAULIC FILTER', price: 35.20, prefix: 'N' },
            { partNumber: 'BF7681', description: 'FUEL FILTER', price: 22.80, prefix: 'P' },
            { partNumber: 'WF2073', description: 'WATER SEPARATOR', price: 18.95, prefix: 'N' },
            { partNumber: 'RE504836', description: 'ENGINE OIL FILTER', price: 15.60, prefix: 'N' },
            { partNumber: 'AR103033', description: 'SEAL KIT', price: 67.40, prefix: 'R' },
            { partNumber: 'T19044', description: 'TRANSMISSION FILTER', price: 42.30, prefix: 'P' },
            { partNumber: 'AL78405', description: 'ALTERNATOR BELT', price: 25.15, prefix: 'S' },
            { partNumber: 'RE508202', description: 'COOLANT FILTER', price: 31.85, prefix: 'N' },
            { partNumber: 'AH128449', description: 'CABIN AIR FILTER', price: 19.75, prefix: 'P' }
        ];

        // Variable to store added parts for current kit
        let addedParts = [];

        // Pagination variables
        let currentPage = 1;
        let itemsPerPage = 3;
        let totalItems = kitData.length;
        let totalPages = Math.ceil(totalItems / itemsPerPage);
        let currentView = 'card'; // 'card' or 'table'
        let filteredData = kitData; // Store filtered data for pagination

        // Search functionality for Kit Part Numbers
        function setupKitPartSearch() {
            const input = document.getElementById('kitPartNumber');
            const dropdown = document.getElementById('kitPartDropdown');

            // Enable typing search as well as icon search
            input.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                if (value.length === 0) {
                    dropdown.classList.remove('show');
                    // Disable part search when kit part is cleared
                    disablePartSearch();
                    return;
                }

                const filtered = kitPartNumbers.filter(kit =>
                    kit.partNumber.toLowerCase().includes(value) ||
                    kit.description.toLowerCase().includes(value)
                );

                if (filtered.length > 0) {
                    dropdown.innerHTML = filtered.map(kit => `
                        <div class="search-dropdown-item" onclick="selectKitPart('${kit.partNumber}', '${kit.description}')">
                            <div class="search-dropdown-item-main">${kit.partNumber}</div>
                            <div class="search-dropdown-item-sub">${kit.description}</div>
                        </div>
                    `).join('');
                    dropdown.classList.add('show');
                } else {
                    dropdown.classList.remove('show');
                }
            });

            input.addEventListener('blur', function() {
                setTimeout(() => dropdown.classList.remove('show'), 200);
            });

            // Check if kit part is valid and enable part search
            input.addEventListener('change', function() {
                const value = this.value;
                const validKit = kitPartNumbers.find(kit => kit.partNumber === value);
                if (validKit) {
                    document.getElementById('kitPartDescription').value = validKit.description;
                    enablePartSearch();
                } else {
                    disablePartSearch();
                }
            });
        }

        // Search functionality for Part Numbers
        function setupPartNumberSearch() {
            const input = document.getElementById('partNumber');
            const dropdown = document.getElementById('partNumberDropdown');

            // Enable typing search when part search is enabled
            input.addEventListener('input', function() {
                if (input.disabled) return;

                const value = this.value.toLowerCase();
                if (value.length === 0) {
                    dropdown.classList.remove('show');
                    return;
                }

                // Filter out already added parts
                const addedPartNumbers = addedParts.map(p => p.partNumber);
                const filtered = partNumbers.filter(part =>
                    !addedPartNumbers.includes(part.partNumber) &&
                    (part.partNumber.toLowerCase().includes(value) ||
                     part.description.toLowerCase().includes(value))
                );

                if (filtered.length > 0) {
                    dropdown.innerHTML = filtered.map(part => `
                        <div class="search-dropdown-item" onclick="selectPart('${part.partNumber}', '${part.description}', ${part.price}, '${part.prefix}')">
                            <div class="search-dropdown-item-main">[${part.prefix}] ${part.partNumber}</div>
                            <div class="search-dropdown-item-sub">${part.description} - $${part.price}</div>
                        </div>
                    `).join('');
                    dropdown.classList.add('show');
                } else {
                    const message = addedPartNumbers.length > 0 ?
                        'No available parts found (excluding already added parts)' :
                        'No parts found';
                    dropdown.innerHTML = `<div class="search-dropdown-item" style="color: var(--text-tertiary); font-style: italic;">${message}</div>`;
                    dropdown.classList.add('show');
                }
            });

            input.addEventListener('blur', function() {
                setTimeout(() => dropdown.classList.remove('show'), 200);
            });
        }

        // Disable part search functionality
        function disablePartSearch() {
            const partInput = document.getElementById('partNumber');
            const partSearchBtn = document.getElementById('partSearchBtn');
            const partDescription = document.getElementById('partDescription');
            const prefix = document.getElementById('prefix');

            partInput.disabled = true;
            partSearchBtn.disabled = true;
            partInput.value = '';
            partDescription.value = '';
            prefix.value = '';

            // Hide dropdown if open
            document.getElementById('partNumberDropdown').classList.remove('show');
        }

        // Show kit part search dropdown
        function showKitPartSearch() {
            const dropdown = document.getElementById('kitPartDropdown');
            dropdown.innerHTML = kitPartNumbers.map(kit => `
                <div class="search-dropdown-item" onclick="selectKitPart('${kit.partNumber}', '${kit.description}')">
                    <div class="search-dropdown-item-main">${kit.partNumber}</div>
                    <div class="search-dropdown-item-sub">${kit.description}</div>
                </div>
            `).join('');
            dropdown.classList.add('show');
        }

        // Select kit part from dropdown
        function selectKitPart(partNumber, description) {
            document.getElementById('kitPartNumber').value = partNumber;
            document.getElementById('kitPartDescription').value = description;
            document.getElementById('kitPartDropdown').classList.remove('show');

            // Enable part search after kit part is selected
            enablePartSearch();
        }

        // Enable part search functionality
        function enablePartSearch() {
            const partInput = document.getElementById('partNumber');
            const partSearchBtn = document.getElementById('partSearchBtn');

            partInput.disabled = false;
            partSearchBtn.disabled = false;

            // Focus on part number field
            setTimeout(() => partInput.focus(), 100);
        }

        // Show part search dropdown
        function showPartSearch() {
            const kitPartNumber = document.getElementById('kitPartNumber').value;
            if (!kitPartNumber) {
                showNotification('Please select a Kit Part # first', 'warning');
                return;
            }

            const dropdown = document.getElementById('partNumberDropdown');
            // Filter out already added parts
            const addedPartNumbers = addedParts.map(p => p.partNumber);
            const availableParts = partNumbers.filter(part => !addedPartNumbers.includes(part.partNumber));

            if (availableParts.length > 0) {
                dropdown.innerHTML = availableParts.map(part => `
                    <div class="search-dropdown-item" onclick="selectPart('${part.partNumber}', '${part.description}', ${part.price}, '${part.prefix}')">
                        <div class="search-dropdown-item-main">[${part.prefix}] ${part.partNumber}</div>
                        <div class="search-dropdown-item-sub">${part.description} - $${part.price}</div>
                    </div>
                `).join('');
            } else {
                dropdown.innerHTML = `<div class="search-dropdown-item" style="color: var(--text-tertiary); font-style: italic;">All available parts have been added to this kit</div>`;
            }
            dropdown.classList.add('show');
        }

        // Select part from dropdown
        function selectPart(partNumber, description, price, prefix) {
            document.getElementById('partNumber').value = partNumber;
            document.getElementById('partDescription').value = description;
            document.getElementById('prefix').value = prefix;
            document.getElementById('partNumberDropdown').classList.remove('show');
        }

        // Add part to kit
        function addPartToKit() {
            const prefix = document.getElementById('prefix').value;
            const partNumber = document.getElementById('partNumber').value.trim();
            const description = document.getElementById('partDescription').value.trim();
            const quantity = parseInt(document.getElementById('quantity').value) || 1;

            if (!partNumber || !description) {
                showNotification('Please enter part number and description', 'error');
                return;
            }

            // Check for duplicate parts
            const existingPart = addedParts.find(p => p.partNumber === partNumber);
            if (existingPart) {
                showNotification(`Part ${partNumber} is already added to this kit. Please select a different part.`, 'warning');
                return;
            }

            // Add new part
            addedParts.push({
                id: Date.now(),
                prefix,
                partNumber,
                description,
                quantity
            });

            // Clear part form fields
            document.getElementById('prefix').value = '';
            document.getElementById('partNumber').value = '';
            document.getElementById('partDescription').value = '';
            document.getElementById('quantity').value = '1';

            // Update parts table
            updatePartsTable();
            showNotification('Part added to kit', 'success');
        }

        // Update parts table
        function updatePartsTable(filteredParts = null) {
            const tableSection = document.getElementById('partsTableSection');
            const tableBody = document.getElementById('addedPartsTableBody');
            const partsToShow = filteredParts || addedParts;

            if (addedParts.length === 0) {
                tableSection.style.display = 'none';
                // Adjust layout when no parts
                adjustKitBomLayout(false);
                return;
            }

            tableSection.style.display = 'block';
            // Adjust layout when parts are present
            adjustKitBomLayout(true);

            tableBody.innerHTML = partsToShow.map(part => `
                <tr>
                    <td style="text-align: center; font-weight: 500;">${part.prefix}</td>
                    <td title="${part.partNumber}">${part.partNumber}</td>
                    <td title="${part.description}" style="word-wrap: break-word; word-break: break-word;">${part.description}</td>
                    <td style="text-align: center;">
                        <input type="number" value="${part.quantity}" min="1"
                               onchange="updatePartQuantity(${part.id}, this.value)"
                               style="width: 50px; padding: 4px; text-align: center; font-size: 12px;">
                    </td>
                    <td style="text-align: center;">
                        <button class="btn-icon-sm" onclick="removePartFromKit(${part.id})" title="Remove Part">
                            <span class="material-icons" style="font-size: 16px;">delete</span>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Update part quantity
        function updatePartQuantity(partId, newQuantity) {
            const part = addedParts.find(p => p.id === partId);
            if (part) {
                part.quantity = parseInt(newQuantity) || 1;
                updatePartsTable();
            }
        }

        // Remove part from kit
        function removePartFromKit(partId) {
            addedParts = addedParts.filter(p => p.id !== partId);
            updatePartsTable();
            showNotification('Part removed from kit', 'info');
        }

        // Search added parts
        function searchAddedParts(searchTerm) {
            if (!searchTerm || searchTerm.trim() === '') {
                // Show all parts if search is empty
                updatePartsTable();
                return;
            }

            const filteredParts = addedParts.filter(part =>
                part.partNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                part.description.toLowerCase().includes(searchTerm.toLowerCase())
            );

            updatePartsTable(filteredParts);
        }

        // Adjust Kit BOM layout based on whether parts are present
        function adjustKitBomLayout(hasParts) {
            const formSection = document.querySelector('.kit-form-section');
            const partsSection = document.querySelector('.kit-parts-section');

            if (hasParts) {
                // Show two-column layout
                formSection.style.flex = '1';
                partsSection.style.display = 'block';
            } else {
                // Show single column layout (form only)
                formSection.style.flex = 'none';
                formSection.style.width = '100%';
                partsSection.style.display = 'none';
            }
        }

        // Theme Management
        function initializeTheme() {
            const savedTheme = localStorage.getItem('kitbom-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('kitbom-theme', newTheme);
            updateThemeIcon(newTheme);
            const themeName = newTheme === 'dark' ? 'Black' : 'White';
            showNotification(`Switched to ${themeName} Theme`, 'success');
        }

        function updateThemeIcon(theme) {
            const themeIcon = document.querySelector('#themeToggle .material-icons');
            const themeToggle = document.getElementById('themeToggle');

            if (theme === 'dark') {
                themeIcon.textContent = 'light_mode';
                themeToggle.title = 'Switch to White Theme';
            } else {
                themeIcon.textContent = 'dark_mode';
                themeToggle.title = 'Switch to Black Theme';
            }
        }

        // Modal Management
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';

                // Reset form state if closing new kit modal
                if (modalId === 'newKitModal') {
                    disablePartSearch();
                    addedParts = [];
                    updatePartsTable();

                    // Hide any open dropdowns
                    document.getElementById('kitPartDropdown').classList.remove('show');
                    document.getElementById('partNumberDropdown').classList.remove('show');
                }
            }
        }

        // Notification System
        function showNotification(message, type = 'info', duration = 3000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span class="material-icons">${getNotificationIcon(type)}</span>
                    <span>${message}</span>
                </div>
            `;

            container.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Hide and remove notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => container.removeChild(notification), 300);
            }, duration);
        }

        function getNotificationIcon(type) {
            switch (type) {
                case 'success': return 'check_circle';
                case 'error': return 'error';
                case 'warning': return 'warning';
                default: return 'info';
            }
        }

        // Enhanced Kit Management Functions
        function saveKit() {
            const form = document.getElementById('newKitForm');
            const formData = new FormData(form);
            const editId = form.dataset.editId;

            // Validate required fields
            if (!formData.get('kitPartNumber') || !formData.get('kitPartDescription')) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            if (editId) {
                // Update existing kit
                const kitIndex = kitData.findIndex(k => k.id == editId);
                if (kitIndex !== -1) {
                    kitData[kitIndex] = {
                        ...kitData[kitIndex],
                        partNumber: formData.get('kitPartNumber'),
                        description: formData.get('kitPartDescription'),
                        isActive: formData.get('isActive') === 'true',
                        parts: [...addedParts] // Update parts
                    };
                    showNotification('Kit BOM updated successfully!', 'success');
                }
            } else {
                // Add new kit
                const newKit = {
                    id: Math.max(...kitData.map(k => k.id), 0) + 1,
                    partNumber: formData.get('kitPartNumber'),
                    description: formData.get('kitPartDescription'),
                    type: 'N', // Default type
                    category: 'NEW',
                    isActive: formData.get('isActive') === 'true',
                    parts: [...addedParts] // Include added parts
                };

                kitData.push(newKit);
                showNotification(`Kit BOM saved successfully with ${addedParts.length} parts!`, 'success');

                // Set edit mode after saving new kit
                form.dataset.editId = newKit.id;
                document.querySelector('#newKitModal .modal-title').textContent = 'Edit Kit BOM';
            }

            renderDataViews();

            // Switch to edit mode - hide Save button, show Edit button
            document.getElementById('saveKitBtn').style.display = 'none';
            document.getElementById('editKitBtn').style.display = 'inline-block';

            // Disable form fields
            disableKitFormFields();
        }

        // Enable edit mode for Kit BOM
        function enableEditMode() {
            // Show Save button, hide Edit button
            document.getElementById('saveKitBtn').style.display = 'inline-block';
            document.getElementById('editKitBtn').style.display = 'none';

            // Enable form fields
            enableKitFormFields();
        }

        // Disable kit form fields
        function disableKitFormFields() {
            document.getElementById('kitPartNumber').disabled = true;
            document.getElementById('kitPartDescription').disabled = true;
            document.getElementById('isActive').disabled = true;

            // Disable part search
            disablePartSearch();

            // Disable add part button
            const addPartBtn = document.querySelector('button[onclick="addPartToKit()"]');
            if (addPartBtn) addPartBtn.disabled = true;
        }

        // Enable kit form fields
        function enableKitFormFields() {
            document.getElementById('kitPartNumber').disabled = false;
            document.getElementById('kitPartDescription').disabled = false;
            document.getElementById('isActive').disabled = false;

            // Enable part search if kit part is selected
            const kitPartNumber = document.getElementById('kitPartNumber').value;
            if (kitPartNumber) {
                enablePartSearch();
            }

            // Enable add part button
            const addPartBtn = document.querySelector('button[onclick="addPartToKit()"]');
            if (addPartBtn) addPartBtn.disabled = false;
        }

        function saveAndExit() {
            const form = document.getElementById('newKitForm');
            const formData = new FormData(form);

            if (!formData.get('kitPartNumber') || !formData.get('kitPartDescription')) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            // Show confirmation dialog
            if (confirm('Are you sure you want to save and exit?')) {
                saveKit();
                closeModal('newKitModal');
                form.reset();
            }
        }



        // Search Functions
        function performAdvancedSearch() {
            const form = document.getElementById('advancedSearchForm');
            const formData = new FormData(form);

            const column = formData.get('searchColumn');
            const operator = formData.get('searchOperator');
            const value = formData.get('searchValue');
            const condition = formData.get('searchCondition');

            // Detailed validation with specific error messages
            const missingFields = [];

            if (!column || column === '' || column === '--Select--') {
                missingFields.push('Column');
            }

            if (!operator || operator === '' || operator === '--Select--') {
                missingFields.push('Operator');
            }

            if (!value || value.trim() === '') {
                missingFields.push('Value');
            }

            if (!condition || condition === '' || condition === '--Select--') {
                missingFields.push('Condition');
            }

            // Show specific error message for missing fields
            if (missingFields.length > 0) {
                const fieldList = missingFields.join(', ');
                const message = `Please select/enter the following required fields: ${fieldList}`;
                showNotification(message, 'error', 5000);

                // Highlight missing fields
                highlightMissingFields(missingFields);
                return;
            }

            // Clear any field highlighting
            clearFieldHighlighting();

            // Simulate search
            showNotification(`Searching ${column} ${operator} "${value}"...`, 'info');
            closeModal('advancedSearchModal');

            // Filter kit data based on search criteria
            filterKitData(column, operator, value);
        }

        function filterKitData(column, operator, value) {
            let filteredData = kitData.filter(kit => {
                let fieldValue = '';
                switch (column) {
                    case 'partNumber': fieldValue = kit.partNumber; break;
                    case 'description': fieldValue = kit.description; break;
                    case 'type': fieldValue = kit.type; break;
                    case 'category': fieldValue = kit.category; break;
                    case 'status': fieldValue = kit.isActive ? 'Active' : 'Inactive'; break;
                }

                switch (operator) {
                    case 'equals': return fieldValue.toLowerCase() === value.toLowerCase();
                    case 'contains': return fieldValue.toLowerCase().includes(value.toLowerCase());
                    case 'startsWith': return fieldValue.toLowerCase().startsWith(value.toLowerCase());
                    case 'endsWith': return fieldValue.toLowerCase().endsWith(value.toLowerCase());
                    case 'notEquals': return fieldValue.toLowerCase() !== value.toLowerCase();
                    default: return true;
                }
            });

            renderDataViews(filteredData);
            showNotification(`Found ${filteredData.length} matching items`, 'success');
        }

        // Highlight missing fields with red border
        function highlightMissingFields(missingFields) {
            // Clear previous highlighting
            clearFieldHighlighting();

            const fieldMap = {
                'Column': 'searchColumn',
                'Operator': 'searchOperator',
                'Value': 'searchValue',
                'Condition': 'searchCondition'
            };

            missingFields.forEach(fieldName => {
                const fieldId = fieldMap[fieldName];
                const field = document.getElementById(fieldId);
                if (field) {
                    field.style.borderColor = '#ef4444';
                    field.style.borderWidth = '2px';
                    field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
                }
            });

            // Remove highlighting after 4 seconds
            setTimeout(clearFieldHighlighting, 4000);
        }

        // Clear field highlighting
        function clearFieldHighlighting() {
            const fields = ['searchColumn', 'searchOperator', 'searchValue', 'searchCondition'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.style.borderColor = '';
                    field.style.borderWidth = '';
                    field.style.boxShadow = '';
                }
            });
        }

        function addFilter() {
            showNotification('Filter added to search criteria', 'info');
        }

        function removeFilter() {
            showNotification('Filter removed from search criteria', 'info');
        }

        // Export Functions
        function performExport() {
            const format = document.querySelector('input[name="exportFormat"]:checked').value;
            const includeHeaders = document.getElementById('includeHeaders').checked;
            const includeActiveOnly = document.getElementById('includeActiveOnly').checked;
            const includeTimestamp = document.getElementById('includeTimestamp').checked;

            let dataToExport = includeActiveOnly ? kitData.filter(kit => kit.isActive) : kitData;

            switch (format) {
                case 'excel':
                    exportToExcel(dataToExport, includeHeaders, includeTimestamp);
                    break;
                case 'word':
                    exportToWord(dataToExport, includeHeaders, includeTimestamp);
                    break;
                case 'pdf':
                    exportToPDF(dataToExport, includeHeaders, includeTimestamp);
                    break;
            }

            closeModal('exportModal');
        }

        // Export to Excel
        function exportToExcel(data, includeHeaders, includeTimestamp) {
            try {
                const ws_data = [];

                if (includeHeaders) {
                    ws_data.push(['Part Number', 'Description', 'Type', 'Category', 'Status']);
                }

                data.forEach(kit => {
                    ws_data.push([
                        kit.partNumber,
                        kit.description,
                        kit.type,
                        kit.category,
                        kit.isActive ? 'Active' : 'Inactive'
                    ]);
                });

                if (includeTimestamp) {
                    ws_data.push([]);
                    ws_data.push(['Exported on:', new Date().toLocaleString()]);
                }

                const ws = XLSX.utils.aoa_to_sheet(ws_data);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'Kit BOM');

                XLSX.writeFile(wb, `Kit_BOM_${new Date().toISOString().split('T')[0]}.xlsx`);
                showNotification('Excel file exported successfully!', 'success');
            } catch (error) {
                showNotification('Error exporting to Excel: ' + error.message, 'error');
            }
        }

        // Export to Word
        function exportToWord(data, includeHeaders, includeTimestamp) {
            try {
                // Create table rows
                const tableRows = [];

                if (includeHeaders) {
                    tableRows.push(new docx.TableRow({
                        children: [
                            new docx.TableCell({ children: [new docx.Paragraph("Part Number")] }),
                            new docx.TableCell({ children: [new docx.Paragraph("Description")] }),
                            new docx.TableCell({ children: [new docx.Paragraph("Type")] }),
                            new docx.TableCell({ children: [new docx.Paragraph("Category")] }),
                            new docx.TableCell({ children: [new docx.Paragraph("Status")] })
                        ]
                    }));
                }

                data.forEach(kit => {
                    tableRows.push(new docx.TableRow({
                        children: [
                            new docx.TableCell({ children: [new docx.Paragraph(kit.partNumber)] }),
                            new docx.TableCell({ children: [new docx.Paragraph(kit.description)] }),
                            new docx.TableCell({ children: [new docx.Paragraph(kit.type)] }),
                            new docx.TableCell({ children: [new docx.Paragraph(kit.category)] }),
                            new docx.TableCell({ children: [new docx.Paragraph(kit.isActive ? 'Active' : 'Inactive')] })
                        ]
                    }));
                });

                const doc = new docx.Document({
                    sections: [{
                        children: [
                            new docx.Paragraph({
                                text: "Kit BOM Report",
                                heading: docx.HeadingLevel.HEADING_1
                            }),
                            includeTimestamp ? new docx.Paragraph(`Generated on: ${new Date().toLocaleString()}`) : null,
                            new docx.Table({
                                rows: tableRows,
                                width: { size: 100, type: docx.WidthType.PERCENTAGE }
                            })
                        ].filter(Boolean)
                    }]
                });

                docx.Packer.toBlob(doc).then(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `Kit_BOM_${new Date().toISOString().split('T')[0]}.docx`;
                    a.click();
                    URL.revokeObjectURL(url);
                    showNotification('Word document exported successfully!', 'success');
                });
            } catch (error) {
                showNotification('Error exporting to Word: ' + error.message, 'error');
            }
        }

        // Export to PDF
        function exportToPDF(data, includeHeaders, includeTimestamp) {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Title
                doc.setFontSize(20);
                doc.text('Kit BOM Report', 20, 20);

                if (includeTimestamp) {
                    doc.setFontSize(12);
                    doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, 35);
                }

                // Prepare table data
                const tableData = [];
                if (includeHeaders) {
                    tableData.push(['Part Number', 'Description', 'Type', 'Category', 'Status']);
                }

                data.forEach(kit => {
                    tableData.push([
                        kit.partNumber,
                        kit.description.length > 30 ? kit.description.substring(0, 30) + '...' : kit.description,
                        kit.type,
                        kit.category,
                        kit.isActive ? 'Active' : 'Inactive'
                    ]);
                });

                // Add table (using autoTable if available, otherwise simple text)
                if (typeof doc.autoTable === 'function') {
                    doc.autoTable({
                        head: includeHeaders ? [tableData[0]] : [],
                        body: includeHeaders ? tableData.slice(1) : tableData,
                        startY: includeTimestamp ? 45 : 30
                    });
                } else {
                    // Fallback to simple text layout
                    let yPosition = includeTimestamp ? 50 : 35;
                    doc.setFontSize(10);
                    tableData.forEach(row => {
                        doc.text(row.join(' | '), 20, yPosition);
                        yPosition += 10;
                    });
                }

                doc.save(`Kit_BOM_${new Date().toISOString().split('T')[0]}.pdf`);
                showNotification('PDF exported successfully!', 'success');
            } catch (error) {
                showNotification('Error exporting to PDF: ' + error.message, 'error');
            }
        }

        // Modern View Management
        // currentView variable declared above with pagination variables

        // Pagination Functions
        function updatePagination() {
            totalItems = filteredData.length;
            totalPages = Math.ceil(totalItems / itemsPerPage);

            // Ensure current page is valid
            if (currentPage > totalPages) {
                currentPage = Math.max(1, totalPages);
            }

            updatePaginationInfo();
            updatePaginationControls();
            renderCurrentView();
        }

        function updatePaginationInfo() {
            const startItem = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
            const endItem = Math.min(currentPage * itemsPerPage, totalItems);

            document.getElementById('paginationInfo').textContent =
                `Showing ${startItem}-${endItem} of ${totalItems} items`;
        }

        function updatePaginationControls() {
            const firstBtn = document.getElementById('firstPageBtn');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');
            const lastBtn = document.getElementById('lastPageBtn');

            // Enable/disable navigation buttons
            firstBtn.disabled = currentPage === 1;
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages || totalPages === 0;
            lastBtn.disabled = currentPage === totalPages || totalPages === 0;

            // Generate page numbers
            generatePageNumbers();
        }

        function generatePageNumbers() {
            const numbersContainer = document.getElementById('paginationNumbers');
            numbersContainer.innerHTML = '';

            if (totalPages <= 1) return;

            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust start page if we're near the end
            if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // Add first page and ellipsis if needed
            if (startPage > 1) {
                numbersContainer.appendChild(createPageButton(1));
                if (startPage > 2) {
                    numbersContainer.appendChild(createEllipsis());
                }
            }

            // Add visible page numbers
            for (let i = startPage; i <= endPage; i++) {
                numbersContainer.appendChild(createPageButton(i));
            }

            // Add ellipsis and last page if needed
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    numbersContainer.appendChild(createEllipsis());
                }
                numbersContainer.appendChild(createPageButton(totalPages));
            }
        }

        function createPageButton(pageNum) {
            const button = document.createElement('button');
            button.className = `pagination-number ${pageNum === currentPage ? 'active' : ''}`;
            button.textContent = pageNum;
            button.onclick = () => goToPage(pageNum);
            return button;
        }

        function createEllipsis() {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'pagination-ellipsis';
            ellipsis.textContent = '...';
            return ellipsis;
        }

        function goToPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                updatePagination();
            }
        }

        function goToFirstPage() {
            goToPage(1);
        }

        function goToPreviousPage() {
            goToPage(currentPage - 1);
        }

        function goToNextPage() {
            goToPage(currentPage + 1);
        }

        function goToLastPage() {
            goToPage(totalPages);
        }

        function changeItemsPerPage(newItemsPerPage) {
            itemsPerPage = parseInt(newItemsPerPage);
            currentPage = 1; // Reset to first page
            updatePagination();
        }

        function getPaginatedData() {
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            return filteredData.slice(startIndex, endIndex);
        }

        function renderCurrentView() {
            const paginatedData = getPaginatedData();

            if (currentView === 'card') {
                renderCardView(paginatedData);
            } else {
                renderTableView(paginatedData);
            }
        }

        // Selection Management
        let selectedKits = new Set();

        // Render Data Views
        function renderDataViews(data = kitData) {
            // Update filtered data for pagination
            filteredData = data;
            currentPage = 1; // Reset to first page when data changes
            updatePagination();
        }

        // Render Modern Card View with Horizontal Scroll
        function renderCardView(data = kitData) {
            const cardView = document.getElementById('cardView');

            if (data.length === 0) {
                cardView.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon material-icons">inventory_2</div>
                        <h3 class="empty-state-title">No Kit Items Found</h3>
                        <p class="empty-state-description">Try adjusting your search criteria or add new kit items to get started.</p>
                        <button class="btn btn-primary" onclick="openModal('newKitModal')">
                            <span class="material-icons">add</span>
                            Add First Kit
                        </button>
                    </div>
                `;
                return;
            }

            // Ensure the scroll container structure exists
            if (!document.getElementById('cardsContainer')) {
                cardView.innerHTML = `
                    <div class="cards-scroll-container">
                        <div class="cards-horizontal-grid" id="cardsContainer">
                        </div>
                    </div>
                `;
            }

            const cardsContainer = document.getElementById('cardsContainer');
            cardsContainer.innerHTML = '';

            data.forEach(kit => {
                const card = document.createElement('div');
                card.className = `kit-card ${selectedKits.has(kit.id) ? 'selected' : ''}`;
                card.onclick = (e) => {
                    if (!e.target.closest('.kit-card-checkbox') && !e.target.closest('.kit-card-edit')) {
                        viewKit(kit.id);
                    }
                };
                    //         <div class="kit-card-edit" onclick="event.stopPropagation(); editKit(${kit.id})" title="Edit Kit">
                    //     <span class="material-icons" style="font-size: 14px;">edit</span>
                    // </div>
                card.innerHTML = `
                    <div class="kit-card-checkbox" onclick="event.stopPropagation(); toggleKitSelection(${kit.id})">
                        ${selectedKits.has(kit.id) ? '<span class="material-icons" style="font-size: 14px;">check</span>' : ''}
                    </div>

                    <div class="kit-card-header">
                        <div class="kit-part-number">
                            <div class="detail-label">Kit Part#:</div>
                            <div class="detail-value">${kit.partNumber}</div>
                        </div>
                        <div class="status-badge ${kit.isActive ? 'status-active' : 'status-inactive'}">
                            <span class="material-icons">${kit.isActive ? 'check_circle' : 'cancel'}</span>
                            ${kit.isActive ? 'Active' : 'Inactive'}
                        </div>
                    </div>
                    <div class="kit-description">
                        <div class="detail-label">Kit Part Description:</div>
                        <div class="detail-value">${kit.description}</div>
                    </div>
                    <div class="kit-details">
                            <div class="detail-label">Prefix:</div>
                            <div class="detail-value">${kit.type}</div>
                    </div>
                `;
                cardsContainer.appendChild(card);
            });
        }

        // Render Modern Table View
        function renderTableView(data = kitData) {
            const tableBody = document.getElementById('tableBody');
            tableBody.innerHTML = '';

            if (data.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="4" style="text-align: center; padding: var(--space-16);">
                            <div class="empty-state">
                                <div class="empty-state-icon material-icons">inventory_2</div>
                                <h3 class="empty-state-title">No Kit Items Found</h3>
                                <p class="empty-state-description">Try adjusting your search criteria or add new kit items.</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            data.forEach(kit => {
                const row = document.createElement('tr');
                row.style.cursor = 'pointer';
                row.onclick = (e) => {
                    viewKit(kit.id);
                };
                row.innerHTML = `
                    <td>
                        <span style="font-family: var(--font-family-mono); font-weight: var(--font-semibold); color: var(--text-primary);">
                            ${kit.partNumber}
                        </span>
                    </td>
                    <td>${kit.description}</td>
                    <td>
                        <span style="font-family: var(--font-family-mono); background: var(--surface-elevated); padding: var(--space-1) var(--space-2); border-radius: var(--radius-sm);">
                            ${kit.type}
                        </span>
                    </td>
                    <td>
                        <div class="status-badge ${kit.isActive ? 'status-active' : 'status-inactive'}">
                            <span class="material-icons">${kit.isActive ? 'check_circle' : 'cancel'}</span>
                            ${kit.isActive ? 'Active' : 'Inactive'}
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Selection Management Functions
        function toggleKitSelection(id) {
            if (selectedKits.has(id)) {
                selectedKits.delete(id);
            } else {
                selectedKits.add(id);
            }
            updateSelectionUI();
            renderCurrentView(); // Use renderCurrentView to maintain pagination
        }

        function selectAllKits() {
            filteredData.forEach(kit => selectedKits.add(kit.id));
            updateSelectionUI();
            renderCurrentView(); // Use renderCurrentView to maintain pagination
        }

        function clearSelection() {
            selectedKits.clear();
            updateSelectionUI();
            renderCurrentView(); // Use renderCurrentView to maintain pagination
        }

        function updateSelectionUI() {
            const count = selectedKits.size;
            const totalCount = filteredData.length;
            const deleteButton = document.getElementById('deleteBtn');
            const deleteButtonText = document.getElementById('deleteButtonText');
            const deleteCount = document.getElementById('deleteCount');
            const selectAllBtn = document.getElementById('selectAllBtn');

            if (count > 0) {
                deleteButtonText.textContent = 'Delete Selected';
                deleteCount.textContent = count;
                deleteCount.style.display = 'inline-block';
                deleteButton.classList.add('btn-danger');
                deleteButton.classList.remove('btn-secondary');
            } else {
                deleteButtonText.textContent = 'Delete';
                deleteCount.style.display = 'none';
                deleteButton.classList.remove('btn-danger');
                deleteButton.classList.add('btn-secondary');
            }

            // Update Select All button
            if (selectAllBtn) {
                if (count === totalCount && totalCount > 0) {
                    selectAllBtn.innerHTML = '<span class="material-icons">deselect</span>Clear Selection';
                } else {
                    selectAllBtn.innerHTML = '<span class="material-icons">select_all</span>Select All';
                }
            }
        }

        // View Toggle Functionality
        function switchView(view) {
            currentView = view;

            // Update toggle buttons
            document.querySelectorAll('.view-toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-view="${view}"]`).classList.add('active');

            // Show/hide views
            const cardView = document.getElementById('cardView');
            const tableView = document.getElementById('tableView');

            if (view === 'card') {
                cardView.style.display = 'block';
                tableView.style.display = 'none';
            } else {
                cardView.style.display = 'none';
                tableView.style.display = 'block';
            }

            // Re-render current view with pagination
            renderCurrentView();
        }

        // Global variable to store current kit being viewed
        let currentKitId = null;

        // Kit Actions
        function viewKit(id) {
            const kit = kitData.find(k => k.id === id);
            if (kit) {
                currentKitId = id;

                // Populate modal with kit details
                document.getElementById('kitDetailsTitle').textContent = `Kit Details - ${kit.partNumber}`;
                document.getElementById('detailPartNumber').textContent = kit.partNumber;
                document.getElementById('detailDescription').textContent = kit.description;
                document.getElementById('detailType').textContent = kit.type;
                document.getElementById('detailCategory').textContent = kit.category;

                // Set status with badge styling
                const statusElement = document.getElementById('detailStatus');
                statusElement.innerHTML = `
                    <div class="status-badge ${kit.isActive ? 'status-active' : 'status-inactive'}">
                        <span class="material-icons">${kit.isActive ? 'check_circle' : 'cancel'}</span>
                        ${kit.isActive ? 'Active' : 'Inactive'}
                    </div>
                `;

                openModal('kitDetailsModal');
            }
        }

        function editKitFromDetails() {
            if (currentKitId) {
                closeModal('kitDetailsModal');
                editKit(currentKitId);
            }
        }

        function deleteKitFromDetails() {
            if (currentKitId) {
                closeModal('kitDetailsModal');
                deleteKit(currentKitId);
            }
        }

        function editKit(id) {
            const kit = kitData.find(k => k.id === id);
            if (kit) {
                // Populate form with kit data
                document.getElementById('kitPartNumber').value = kit.partNumber;
                document.getElementById('kitPartDescription').value = kit.description;
                document.getElementById('isActive').value = kit.isActive.toString();

                // Store the ID for updating
                document.getElementById('newKitForm').dataset.editId = id;

                // Change modal title
                document.querySelector('#newKitModal .modal-title').textContent = 'Edit Kit BOM';

                openModal('newKitModal');

                // Focus first input for better mobile experience (only on desktop)
                setTimeout(() => {
                    const firstInput = document.getElementById('kitPartNumber');
                    if (firstInput && window.innerWidth > 768) {
                        firstInput.focus();
                        firstInput.select();
                    }
                }, 300);
            }
        }

        function deleteKit(id) {
            const kit = kitData.find(k => k.id === id);
            if (kit) {
                const confirmMessage = `Are you sure you want to delete this kit item?\n\nPart Number: ${kit.partNumber}\nDescription: ${kit.description}\nType: ${kit.type}\nCategory: ${kit.category}\n\nThis action cannot be undone.`;

                if (confirm(confirmMessage)) {
                    kitData = kitData.filter(k => k.id !== id);
                    renderDataViews();
                    showNotification(`Kit item "${kit.partNumber}" deleted successfully!`, 'success');
                }
            }
        }

        function deleteSelectedKits() {
            const selectedCount = selectedKits.size;

            if (selectedCount === 0) {
                showNotification('No items selected for deletion. Select cards by clicking the checkbox that appears when you hover over them.', 'warning');
                return;
            }

            // Get selected kit details for confirmation
            const selectedKitDetails = Array.from(selectedKits).map(id => {
                const kit = kitData.find(k => k.id === id);
                return kit ? `• ${kit.partNumber} - ${kit.description}` : '';
            }).filter(Boolean);

            const confirmMessage = `Are you sure you want to delete ${selectedCount} selected kit item${selectedCount > 1 ? 's' : ''}?\n\n${selectedKitDetails.join('\n')}\n\nThis action cannot be undone.`;

            if (confirm(confirmMessage)) {
                // Remove selected items from kitData
                kitData = kitData.filter(kit => !selectedKits.has(kit.id));

                // Clear selection
                selectedKits.clear();
                updateSelectionUI();

                // Re-render views
                renderDataViews();

                showNotification(`${selectedCount} kit item${selectedCount > 1 ? 's' : ''} deleted successfully!`, 'success');
            }
        }

        // Global Search
        function performGlobalSearch() {
            const searchTerm = document.getElementById('globalSearch').value.toLowerCase();
            if (!searchTerm) {
                renderDataViews(kitData); // Show all data
                return;
            }

            const searchResults = kitData.filter(kit =>
                kit.partNumber.toLowerCase().includes(searchTerm) ||
                kit.description.toLowerCase().includes(searchTerm) ||
                kit.category.toLowerCase().includes(searchTerm)
            );

            renderDataViews(searchResults);
            showNotification(`Found ${searchResults.length} items matching "${searchTerm}"`, 'info');
        }

        // Sidebar Toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('sidebar-collapsed');

            // Update toggle icon
            const toggleIcon = document.querySelector('#sidebarToggle .material-icons');
            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.textContent = 'chevron_right';
            } else {
                toggleIcon.textContent = 'chevron_left';
            }
        }

        // Enhanced Mobile Menu Toggle
        function toggleMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            // Prevent body scroll when sidebar is open
            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        // Close Mobile Menu
        function closeMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme
            initializeTheme();

            // Render initial data views
            renderDataViews();

            // Setup search functionality
            setupKitPartSearch();
            setupPartNumberSearch();

            // Theme toggle
            document.getElementById('themeToggle').addEventListener('click', toggleTheme);

            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);

            // Mobile menu toggle
            document.getElementById('mobileMenuToggle').addEventListener('click', toggleMobileMenu);

            // Enhanced window resize handler for responsive behavior and developer tools
            let resizeTimeout;
            window.addEventListener('resize', function() {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');
                const overlay = document.getElementById('sidebarOverlay');
                const width = window.innerWidth;

                // Close mobile menu on desktop resize
                if (width > 768) {
                    sidebar.classList.remove('open', 'mobile-open');
                    overlay.classList.remove('active');
                    document.body.style.overflow = '';
                }

                // Auto-collapse/expand sidebar based on screen size
                if (width <= 1200 && width > 768) {
                    // Medium screens - auto collapse
                    if (!sidebar.classList.contains('collapsed')) {
                        sidebar.classList.add('collapsed');
                        mainContent.classList.add('sidebar-collapsed');
                        const toggleIcon = document.querySelector('#sidebarToggle .material-icons');
                        if (toggleIcon) toggleIcon.textContent = 'chevron_right';
                    }
                } else if (width > 1200) {
                    // Large screens - auto expand
                    if (sidebar.classList.contains('collapsed')) {
                        sidebar.classList.remove('collapsed');
                        mainContent.classList.remove('sidebar-collapsed');
                        const toggleIcon = document.querySelector('#sidebarToggle .material-icons');
                        if (toggleIcon) toggleIcon.textContent = 'chevron_left';
                    }
                }

                // Debounce resize handling for performance
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(handleViewportChange, 150);
            });

            // Handle viewport changes (including developer tools opening/closing)
            function handleViewportChange() {
                const width = window.innerWidth;
                const height = window.innerHeight;

                // Apply horizontal scroll classes based on viewport width
                const elementsToFix = [
                    '.page-actions',
                    '.data-header',
                    '.modal-footer'
                ];

                elementsToFix.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        if (width < 1200) {
                            element.classList.add('dev-tools-flex');
                        } else {
                            element.classList.remove('dev-tools-flex');
                        }
                    });
                });

                // Ensure sidebar stability during viewport changes
                const sidebar = document.querySelector('.sidebar');
                if (sidebar) {
                    // Prevent sidebar collapse during dev tools opening
                    sidebar.style.flexShrink = '0';
                    if (sidebar.classList.contains('collapsed')) {
                        sidebar.style.minWidth = 'var(--sidebar-collapsed)';
                        sidebar.style.width = 'var(--sidebar-collapsed)';
                    } else {
                        sidebar.style.minWidth = 'var(--sidebar-width)';
                        sidebar.style.width = 'var(--sidebar-width)';
                    }
                }

                // Ensure proper scrolling behavior based on viewport
                const cardView = document.querySelector('.card-view');
                const cardsContainer = document.querySelector('.cards-horizontal-grid');
                const dataContainer = document.querySelector('.data-container');
                const dataView = document.querySelector('.data-view');

                if (cardView && cardsContainer && dataContainer && dataView) {
                    // Ensure data container never collapses
                    dataContainer.style.minHeight = '500px';
                    dataContainer.style.flexShrink = '0';
                    dataView.style.minHeight = '400px';
                    dataView.style.flexShrink = '0';

                    // Always use grid layout - CSS handles responsiveness
                    cardView.style.overflowX = 'hidden';
                    cardView.style.overflowY = 'auto';
                    cardsContainer.style.display = 'grid';
                    cardsContainer.style.flexWrap = '';
                    cardsContainer.style.minWidth = '';
                    cardsContainer.style.gridTemplateColumns = '';
                }

                // Ensure table containers have proper scrolling
                const tableContainers = document.querySelectorAll('.table-container');
                tableContainers.forEach(container => {
                    container.style.overflowX = 'auto';
                    container.style.webkitOverflowScrolling = 'touch';
                });
            }

            // Initial responsive setup
            const initialWidth = window.innerWidth;
            const sidebarEl = document.getElementById('sidebar');
            const mainContentEl = document.getElementById('mainContent');

            if (initialWidth <= 1200 && initialWidth > 768) {
                sidebarEl.classList.add('collapsed');
                mainContentEl.classList.add('sidebar-collapsed');
                const toggleIcon = document.querySelector('#sidebarToggle .material-icons');
                if (toggleIcon) toggleIcon.textContent = 'chevron_right';
            }

            // Initial viewport setup
            handleViewportChange();

            // View toggle buttons
            document.querySelectorAll('.view-toggle-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const view = btn.getAttribute('data-view');
                    switchView(view);
                });
            });

            // Button event listeners
            document.getElementById('newKitBtn').addEventListener('click', () => {
                // Reset form for new kit
                const form = document.getElementById('newKitForm');
                form.reset();
                delete form.dataset.editId;
                document.querySelector('#newKitModal .modal-title').textContent = 'Add Kit BOM';

                // Reset button states - show Save, hide Edit
                document.getElementById('saveKitBtn').style.display = 'inline-block';
                document.getElementById('editKitBtn').style.display = 'none';

                // Enable all form fields
                enableKitFormFields();

                // Reset search states
                disablePartSearch();
                addedParts = [];
                updatePartsTable();

                openModal('newKitModal');
            });
            document.getElementById('deleteBtn').addEventListener('click', deleteSelectedKits);
            document.getElementById('selectAllBtn').addEventListener('click', () => {
                if (selectedKits.size === filteredData.length && filteredData.length > 0) {
                    clearSelection();
                    showNotification('Selection cleared', 'info');
                } else {
                    selectAllKits();
                    showNotification(`Selected all ${filteredData.length} items`, 'info');
                }
            });
            document.getElementById('advancedSearchBtn').addEventListener('click', () => openModal('advancedSearchModal'));
            document.getElementById('exportBtn').addEventListener('click', () => openModal('exportModal'));
            document.getElementById('refreshBtn').addEventListener('click', () => {
                renderDataViews();
                showNotification('Data refreshed', 'success');
            });

            // Global search
            const globalSearch = document.getElementById('globalSearch');
            globalSearch.addEventListener('input', performGlobalSearch);
            globalSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performGlobalSearch();
                }
            });

            // Modal backdrop click to close
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.addEventListener('click', function(e) {
                    if (e.target === backdrop) {
                        closeModal(backdrop.id);
                    }
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Escape key to close modals, mobile menu, or clear selection
                if (e.key === 'Escape') {
                    const activeModal = document.querySelector('.modal-backdrop.active');
                    const sidebar = document.getElementById('sidebar');

                    if (activeModal) {
                        closeModal(activeModal.id);
                    } else if (sidebar.classList.contains('open')) {
                        closeMobileMenu();
                    } else if (selectedKits.size > 0) {
                        clearSelection();
                        showNotification('Selection cleared', 'info');
                    }
                }

                // Ctrl+A for select all (only in card view)
                if (e.ctrlKey && e.key === 'a' && currentView === 'card') {
                    e.preventDefault();
                    selectAllKits();
                    showNotification(`Selected all ${kitData.length} items`, 'info');
                }

                // Ctrl+N for new kit
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    openModal('newKitModal');
                }

                // Ctrl+F for search
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    document.getElementById('globalSearch').focus();
                }

                // Ctrl+E for export
                if (e.ctrlKey && e.key === 'e') {
                    e.preventDefault();
                    openModal('exportModal');
                }

                // Delete key for selected items
                if (e.key === 'Delete' && selectedKits.size > 0) {
                    e.preventDefault();
                    deleteSelectedKits();
                }
            });

            // Form validation
            const newKitForm = document.getElementById('newKitForm');
            newKitForm.addEventListener('submit', function(e) {
                e.preventDefault();
                saveKit();
            });

            // Advanced search form
            const advancedSearchForm = document.getElementById('advancedSearchForm');
            advancedSearchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                performAdvancedSearch();
            });

            // Added parts search functionality
            const addedPartsSearch = document.getElementById('addedPartsSearch');
            if (addedPartsSearch) {
                addedPartsSearch.addEventListener('input', function() {
                    searchAddedParts(this.value);
                });
            }

            // Initialize Material Components
            if (window.mdc) {
                // Initialize any MDC components if needed
                const buttons = document.querySelectorAll('.mdc-button');
                buttons.forEach(button => {
                    if (window.mdc.ripple) {
                        window.mdc.ripple.MDCRipple.attachTo(button);
                    }
                });
            }

            // Accessibility improvements
            document.querySelectorAll('button').forEach(button => {
                if (!button.getAttribute('aria-label') && button.title) {
                    button.setAttribute('aria-label', button.title);
                }
            });

            // Auto-save form data to localStorage
            const formInputs = document.querySelectorAll('#newKitForm input, #newKitForm select, #newKitForm textarea');
            formInputs.forEach(input => {
                // Load saved data
                const savedValue = localStorage.getItem(`kitbom-form-${input.name}`);
                if (savedValue && input.type !== 'file') {
                    input.value = savedValue;
                }

                // Save data on change
                input.addEventListener('change', function() {
                    localStorage.setItem(`kitbom-form-${input.name}`, input.value);
                });
            });

            // Mobile menu functionality
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('mobile-open');
                    sidebarOverlay.classList.toggle('active');
                });
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('active');
                });
            }

            // Pagination event listeners
            document.getElementById('firstPageBtn').addEventListener('click', goToFirstPage);
            document.getElementById('prevPageBtn').addEventListener('click', goToPreviousPage);
            document.getElementById('nextPageBtn').addEventListener('click', goToNextPage);
            document.getElementById('lastPageBtn').addEventListener('click', goToLastPage);

            document.getElementById('itemsPerPage').addEventListener('change', function() {
                changeItemsPerPage(this.value);
            });

            // Initialize pagination
            updatePagination();

            console.log('Kit BOM application initialized successfully');
            showNotification('Kit BOM application loaded', 'success', 2000);
        });

        // Service Worker for offline functionality (optional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                // Uncomment to enable service worker
                // navigator.serviceWorker.register('/sw.js')
                //     .then(registration => console.log('SW registered'))
                //     .catch(error => console.log('SW registration failed'));
            });
        }

        // Error handling
        window.addEventListener('error', function(e) {
            console.error('Application error:', e.error);
            showNotification('An error occurred. Please refresh the page.', 'error');
        });

        // Unhandled promise rejection handling
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            showNotification('An error occurred during operation.', 'error');
        });
    </script>
</body>
</html>