<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HCLSoftware Modern UI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800&display=swap');

        :root {
            /* Black and White Theme with Black Accents */
            --bg-color: #FFFFFF;
            --surface-color: #FFFFFF;
            --text-color: #000000;
            --text-secondary: #4B5563;
            --border-color: rgba(0, 0, 0, 0.1);
            --sidebar-bg: linear-gradient(180deg, #111827, #1F2937);
            --sidebar-text: #F9FAFB;
            --accent-color: #000000;
            --accent-hover: #333333;
            --shadow: rgba(0, 0, 0, 0.15);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(0, 0, 0, 0.05);
        }

        body.dark-mode {
            --bg-color: #111827;
            --surface-color: #1F2937;
            --text-color: #F9FAFB;
            --text-secondary: #9CA3AF;
            --border-color: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(31, 41, 55, 0.95);
            --glass-border: rgba(255, 255, 255, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            display: flex;
            min-height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            width: 100%;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 72px;
            background: var(--sidebar-bg);
            padding: 20px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1000;
            will-change: width;
        }

        .sidebar.expanded {
            width: 300px;
            align-items: flex-start;
            padding: 20px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--sidebar-text);
            font-size: 26px;
            cursor: pointer;
            padding: 12px;
            transition: transform 0.3s ease;
        }

        .sidebar-toggle:hover {
            transform: rotate(90deg);
        }

        .sidebar-header {
            display: none;
            margin: 20px 24px 40px;
        }

        .sidebar.expanded .sidebar-header {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .sidebar-header h2 {
            font-size: 22px;
            font-weight: 800;
            color: var(--sidebar-text);
            letter-spacing: 0.5px;
        }

        .sidebar-header img {
            width: 36px;
            height: 36px;
            border-radius: 10px;
        }

        .sidebar-nav ul {
            list-style: none;
            width: 100%;
        }

        .sidebar-nav ul li a {
            display: flex;
            align-items: center;
            gap: 14px;
            padding: 14px 20px;
            color: var(--sidebar-text);
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            border-radius: 12px;
            transition: background 0.3s ease, transform 0.3s ease;
            position: relative;
        }

        .sidebar-nav ul li a:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateX(8px);
        }

        .sidebar-nav ul li.active a {
            background: rgba(255, 255, 255, 0.35);
            font-weight: 700;
        }

        .nav-section h3 {
            font-size: 14px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.7);
            padding: 12px 24px;
            display: none;
            letter-spacing: 0.5px;
        }

        .sidebar.expanded .nav-section h3 {
            display: block;
        }

        .nav-icon {
            font-size: 22px;
            width: 30px;
            text-align: center;
            position: relative;
        }

        .nav-text {
            display: none;
        }

        .sidebar.expanded .nav-text {
            display: inline;
        }

        .nav-icon::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: #1F2937;
            color: #FFFFFF;
            padding: 8px 14px;
            border-radius: 8px;
            font-size: 13px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .sidebar:not(.expanded) .nav-icon:hover::after {
            opacity: 1;
            visibility: visible;
        }

        /* Top Navigation (Mobile) */
        .top-nav {
            display: none;
            background: var(--sidebar-bg);
            padding: 12px 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .top-nav.expanded {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .top-nav-toggle {
            background: none;
            border: none;
            color: var(--sidebar-text);
            font-size: 24px;
            cursor: pointer;
        }

        .top-nav-menu {
            display: none;
            flex-direction: column;
            gap: 10px;
        }

        .top-nav.expanded .top-nav-menu {
            display: flex;
        }

        .top-nav-menu a {
            color: var(--sidebar-text);
            text-decoration: none;
            font-size: 16px;
            padding: 10px;
            border-radius: 8px;
        }

        .top-nav-menu a:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        /* Main Content */
        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 24px;
            overflow-y: auto;
            position: relative;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            padding: 20px 24px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: 14px;
            margin-bottom: 24px;
            border: 1px solid var(--glass-border);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow);
        }

        .search-bar {
            flex-grow: 1;
            position: relative;
        }

        .search-bar input {
            width: 100%;
            padding: 14px 20px 14px 48px;
            border: none;
            border-radius: 12px;
            background: var(--surface-color);
            color: var(--text-color);
            font-size: 16px;
            box-shadow: inset 0 1px 4px var(--shadow);
            transition: box-shadow 0.3s ease, transform 0.3s ease;
        }

        .search-bar input:focus {
            box-shadow: 0 0 0 3px var(--accent-color);
            transform: scale(1.01);
            outline: none;
        }

        .search-bar .fa-search {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 18px;
            transition: color 0.3s ease;
        }

        .search-bar input:focus + .fa-search {
            color: var(--accent-color);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .welcome-text {
            font-size: 16px;
            color: var(--text-secondary);
            font-weight: 400;
        }

        .branch {
            color: var(--text-color);
            text-decoration: none;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
        }

        .branch i {
            color: var(--accent-color);
            font-size: 20px;
            transition: transform 0.3s ease;
        }

        .branch:hover i {
            transform: rotate(180deg);
        }

        .action-btn {
            background: linear-gradient(145deg, var(--accent-color), var(--accent-hover));
            border: none;
            color: #FFFFFF;
            padding: 12px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 4px 8px var(--shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.4s ease, height 0.4s ease;
        }

        .action-btn:hover::after {
            width: 200px;
            height: 200px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px var(--shadow);
        }

        /* Content Area */
        .content-area {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: 14px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--glass-border);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap;
            gap: 16px;
        }

        .breadcrumb {
            font-size: 24px;
            font-weight: 800;
            color: var(--text-color);
            letter-spacing: 0.5px;
        }

        .controls {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            background: linear-gradient(145deg, var(--accent-color), var(--accent-hover));
            color: #FFFFFF;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 8px var(--shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.4s ease, height 0.4s ease;
        }

        .btn:hover::after {
            width: 200px;
            height: 200px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px var(--shadow);
        }

        .btn-secondary {
            background: linear-gradient(145deg, var(--accent-color), var(--accent-hover));
            border: none;
            color: #FFFFFF;
            box-shadow: 0 4px 8px var(--shadow);
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px var(--shadow);
        }

        .btn-cancel {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-color);
            box-shadow: none;
        }

        .btn-cancel:hover {
            background: var(--hover-bg);
            box-shadow: 0 4px 8px var(--shadow);
            transform: translateY(-3px);
        }

        .view-toggle {
            display: flex;
            gap: 12px;
        }

        .view-toggle .btn {
            padding: 12px;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--accent-color);
            color: #FFFFFF;
            box-shadow: 0 3px 6px var(--shadow);
        }

        .view-toggle .btn.active {
            background: var(--accent-color);
            color: #FFFFFF;
            border: 2px solid rgba(255, 252, 252, 0.5);
        }

        .view-toggle .btn:hover {
            transform: scale(1.2);
        }

        /* Table */
        .table-container {
            padding: 24px;
            flex-grow: 1;
            overflow-x: auto;
            transition: opacity 0.4s ease;
        }

        .table-container.hidden {
            opacity: 0;
            display: none;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 16px;
        }

        th,
        td {
            padding: 16px 24px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--glass-bg);
            color: var(--text-secondary);
            font-weight: 700;
            text-transform: uppercase;
            position: sticky;
            top: 0;
            z-index: 10;
            letter-spacing: 0.5px;
        }

        th .fa-sort {
            cursor: pointer;
            margin-left: 10px;
            transition: color 0.3s ease;
        }

        th .fa-sort:hover {
            color: var(--accent-color);
        }

        td {
            color: var(--text-color);
        }

        tr {
            transition: background 0.3s ease;
        }

        tr:hover {
            background: var(--hover-bg);
        }

        td .fa-edit {
            cursor: pointer;
            color: var(--accent-color);
            font-size: 20px;
            transition: transform 0.3s ease;
        }

        td .fa-edit:hover {
            transform: scale(1.4);
        }

        /* Cards */
        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            padding: 24px;
            flex-grow: 1;
            overflow-y: auto;
            transition: opacity 0.4s ease;
        }

        .card-container.hidden {
            opacity: 0;
            display: none;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 14px;
            padding: 20px;
            transition: transform 0.3s ease, filter 0.3s ease, border-color 0.3s ease;
            will-change: transform, filter, border-color;
        }

        .card:hover {
            transform: scale(1.05);
            filter: brightness(1.1);
            border-color: var(--accent-color);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .card-title {
            font-size: 20px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .card-body p {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 12px;
        }

        .card-body strong {
            color: var(--text-color);
            font-weight: 600;
        }

        /* Pagination */
        .pagination {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 20px 24px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 14px;
            position: sticky;
            bottom: 0;
            z-index: 100;
            justify-content: flex-end;
            flex-wrap: wrap;
            margin-top: auto;
        }

        .pagination span {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .pagination input,
        .pagination select {
            padding: 10px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background: var(--surface-color);
            color: var(--text-color);
            font-size: 16px;
            box-shadow: inset 0 1px 3px var(--shadow);
            transition: box-shadow 0.3s ease;
        }

        .pagination input:focus,
        .pagination select:focus {
            box-shadow: 0 0 0 3px var(--accent-color);
            outline: none;
        }

        .pagination input {
            width: 80px;
            text-align: center;
        }

        .view-info {
            font-size: 16px;
            color: var(--text-secondary);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 14px;
            padding: 24px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 4px 16px var(--shadow);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .modal-header h2 {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-color);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .modal-close:hover {
            color: var(--text-color);
        }

        .modal-body {
            display: flex;
            gap: 24px;
        }

        .modal-left,
        .modal-right {
            flex: 1;
        }

        .modal-left form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .modal-left label {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }

        .modal-left input,
        .modal-left select {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background: var(--surface-color);
            color: var(--text-color);
            font-size: 16px;
            box-shadow: inset 0 1px 3px var(--shadow);
            transition: box-shadow 0.3s ease;
        }

        .modal-left input:focus,
        .modal-left select:focus {
            box-shadow: 0 0 0 3px var(--accent-color);
            outline: none;
        }

        .modal-left input:disabled,
        .modal-left select:disabled {
            background: var(--border-color);
            cursor: not-allowed;
        }

        .modal-right {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        /* Accordion Styles */
        .accordion {
            margin-bottom: 24px;
        }

        .accordion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: var(--surface-color);
            border-radius: 12px;
            box-shadow: inset 0 1px 3px var(--shadow);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .accordion-header:hover {
            background: var(--border-color);
        }

        .accordion-header h3 {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-color);
        }

        .accordion-header i {
            font-size: 16px;
            color: var(--text-secondary);
            transition: transform 0.3s ease;
        }

        .accordion-header.active i {
            transform: rotate(180deg);
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }

        .accordion-content.active {
            max-height: 600px; /* Increased to accommodate scrolling */
            padding: 16px 0;
        }

        /* Parts Details and Superseding Parts Details Toggle */
        .parts-details-toggle,
        .superseding-parts-toggle {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .parts-details-toggle .btn,
        .superseding-parts-toggle .btn {
            padding: 8px 16px;
            font-size: 14px;
        }

        .parts-details-toggle .btn.active,
        .superseding-parts-toggle .btn.active {
            background: var(--accent-color);
            color: #FFFFFF;
            border: 2px solid rgba(255, 252, 252, 0.5);
        }

        .parts-details-table,
        .parts-details-card,
        .superseding-parts-table,
        .superseding-parts-card {
            display: none;
        }

        .parts-details-table.active,
        .parts-details-card.active,
        .superseding-parts-table.active,
        .superseding-parts-card.active {
            display: block;
        }

        /* Scrollable Containers for Parts Details and Superseding Parts Details */
        .parts-details-table-container,
        .parts-details-card-container,
        .superseding-parts-table-container,
        .superseding-parts-card-container {
            max-height: 300px; /* Maximum height before scrolling */
            overflow-y: auto; /* Enable vertical scrolling */
            position: relative; /* For sticky headers */
        }

        .parts-details-table table,
        .superseding-parts-table table {
            width: 100%;
            font-size: 14px;
        }

        .parts-details-table th,
        .parts-details-table td,
        .superseding-parts-table th,
        .superseding-parts-table td {
            padding: 12px;
            text-align: left;
        }

        .parts-details-table th,
        .superseding-parts-table th {
            background: var(--glass-bg);
            color: var(--text-secondary);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .parts-details-card-container,
        .superseding-parts-card-container {
            padding: 16px;
            background: var(--surface-color);
            border-radius: 12px;
            box-shadow: inset 0 1px 3px var(--shadow);
        }

        .parts-details-card-container p,
        .superseding-parts-card-container p {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .parts-details-card-container p strong,
        .superseding-parts-card-container p strong {
            color: var(--text-color);
            font-weight: 600;
        }

        /* History Table with Scroll */
        .history-table-container {
            max-height: 200px;
            overflow-y: auto;
            position: relative;
        }

        .history-table-container table {
            width: 100%;
            font-size: 14px;
        }

        .history-table-container th,
        .history-table-container td {
            padding: 12px;
        }

        .history-table-container th {
            position: sticky;
            top: 0;
            background: var(--glass-bg);
            z-index: 10;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar.expanded {
                width: 260px;
            }
            .main-content {
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            .sidebar {
                display: none;
            }
            .top-nav {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
                padding: 16px 20px;
            }
            .search-bar {
                width: 100%;
            }
            .header-actions {
                align-self: flex-end;
            }
            .card-container {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
            .btn,
            .action-btn {
                padding: 10px 20px;
                font-size: 15px;
            }
            .modal-body {
                flex-direction: column;
            }
            .modal-right {
                gap: 16px;
            }
        }

        @media (max-width: 480px) {
            th,
            td,
            .card-body p {
                font-size: 15px;
            }
            .pagination input,
            .pagination select {
                font-size: 15px;
                padding: 8px 12px;
            }
            .card-container {
                grid-template-columns: 1fr;
            }
            .breadcrumb {
                font-size: 20px;
            }
            .modal-content {
                width: 95%;
                padding: 16px;
            }
            .history-table-container table,
            .parts-details-table table,
            .superseding-parts-table table {
                font-size: 13px;
            }
            .parts-details-table th,
            .parts-details-table td,
            .superseding-parts-table th,
            .superseding-parts-table td {
                padding: 8px;
            }
            .parts-details-card-container p,
            .superseding-parts-card-container p {
                font-size: 15px;
            }
            .parts-details-table-container,
            .parts-details-card-container,
            .superseding-parts-table-container,
            .superseding-parts-card-container {
                max-height: 200px; /* Reduced height for smaller screens */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="top-nav">
            <button class="top-nav-toggle" aria-label="Toggle Menu"><i class="fas fa-bars"></i></button>
            <div class="top-nav-menu">
                <a href="#" aria-label="Service Invoice Return">Service Invoice Return</a>
                <a href="#" aria-label="Service Type">Service Type</a>
                <a href="#" aria-label="Movement Type">Movement Type</a>
                <a href="#" aria-label="Part S">Part S</a>
                <a href="#" aria-label="Service">Service</a>
                <a href="#" aria-label="TAMS">TAMS</a>
                <a href="#" aria-label="Dashboard">Dashboard</a>
                <a href="#" aria-label="KPI Report">KPI Report</a>
                <a href="#" aria-label="Contract Management">Contract Management</a>
                <a href="#" aria-label="Digital Catalogue">Digital Catalogue</a>
                <a href="#" aria-label="Reman">Reman</a>
                <a href="#" aria-label="Order Management">Order Management</a>
                <a href="#" aria-label="Field Service">Field Service</a>
                <a href="#" aria-label="Warranty">Warranty</a>
                <a href="#" aria-label="Sales">Sales</a>
            </div>
        </nav>

        <aside class="sidebar">
            <button class="sidebar-toggle" aria-label="Toggle Sidebar"><i class="fas fa-bars"></i></button>
            <div class="sidebar-header">
                <img src="https://via.placeholder.com/36" alt="HCLSoftware Logo">
                <h2>HCLSoftware</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active"><a href="#" aria-label="Service Invoice Return"><i class="fas fa-file-invoice nav-icon" data-tooltip="Service Invoice Return"></i><span class="nav-text">Service Invoice Return</span></a></li>
                    <li><a href="#" aria-label="Service Type"><i class="fas fa-cogs nav-icon" data-tooltip="Service Type"></i><span class="nav-text">Service Type</span></a></li>
                    <li><a href="#" aria-label="Movement Type"><i class="fas fa-exchange-alt nav-icon" data-tooltip="Movement Type"></i><span class="nav-text">Movement Type</span></a></li>
                </ul>
                <div class="nav-section">
                    <h3>Helpdesk</h3>
                    <ul>
                        <li><a href="#" aria-label="Part S"><i class="fas fa-tools nav-icon" data-tooltip="Part S"></i><span class="nav-text">Part S</span></a></li>
                        <li><a href="#" aria-label="Service"><i class="fas fa-headset nav-icon" data-tooltip="Service"></i><span class="nav-text">Service</span></a></li>
                        <li><a href="#" aria-label="TAMS"><i class="fas fa-tasks nav-icon" data-tooltip="TAMS"></i><span class="nav-text">TAMS</span></a></li>
                    </ul>
                </div>
                <div class="nav-section">
                    <h3>Core</h3>
                    <ul>
                        <li><a href="#" aria-label="Dashboard"><i class="fas fa-tachometer-alt nav-icon" data-tooltip="Dashboard"></i><span class="nav-text">Dashboard</span></a></li>
                        <li><a href="#" aria-label="KPI Report"><i class="fas fa-chart-bar nav-icon" data-tooltip="KPI Report"></i><span class="nav-text">KPI Report</span></a></li>
                        <li><a href="#" aria-label="Contract Management"><i class="fas fa-file-contract nav-icon" data-tooltip="Contract Management"></i><span class="nav-text">Contract Management</span></a></li>
                        <li><a href="#" aria-label="Digital Catalogue"><i class="fas fa-book nav-icon" data-tooltip="Digital Catalogue"></i><span class="nav-text">Digital Catalogue</span></a></li>
                        <li><a href="#" aria-label="Reman"><i class="fas fa-recycle nav-icon" data-tooltip="Reman"></i><span class="nav-text">Reman</span></a></li>
                    </ul>
                </div>
                <div class="nav-section">
                    <h3>Special Tools</h3>
                    <ul>
                        <li><a href="#" aria-label="Order Management"><i class="fas fa-shopping-cart nav-icon" data-tooltip="Order Management"></i><span class="nav-text">Order Management</span></a></li>
                        <li><a href="#" aria-label="Field Service"><i class="fas fa-briefcase nav-icon" data-tooltip="Field Service"></i><span class="nav-text">Field Service</span></a></li>
                        <li><a href="#" aria-label="Warranty"><i class="fas fa-shield-alt nav-icon" data-tooltip="Warranty"></i><span class="nav-text">Warranty</span></a></li>
                        <li><a href="#" aria-label="Sales"><i class="fas fa-dollar-sign nav-icon" data-tooltip="Sales"></i><span class="nav-text">Sales</span></a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <main class="main-content">
            <header class="header">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search anything..." aria-label="Search">
                </div>
                <div class="header-actions">
                    <span class="welcome-text">Hello, Admin</span>
                    <a href="#" class="branch" aria-label="Switch Branch">Branch 11 <i class="fas fa-sign-out-alt"></i></a>
                    <button class="action-btn" aria-label="Notifications"><i class="fas fa-bell"></i></button>
                    <button class="action-btn" id="darkModeToggle" aria-label="Toggle Dark Mode"><i class="fas fa-moon"></i></button>
                </div>
            </header>

            <section class="content-area">
                <div class="top-bar">
                    <div class="breadcrumb">Supersession</div>
                    <div class="controls">
                        <button class="btn" aria-label="Create New"><i class="fas fa-plus"></i> Create New</button>
                        <button class="btn btn-secondary" aria-label="Filter"><i class="fas fa-search"></i> Filter</button>
                        <button class="btn btn-secondary" aria-label="Export"><i class="fas fa-file-download"></i> Export</button>
                        <div class="view-toggle">
                            <button id="tableViewBtn" class="btn active" aria-label="Table View"><i class="fas fa-table"></i></button>
                            <button id="cardViewBtn" class="btn" aria-label="Card View"><i class="fas fa-th-large"></i></button>
                        </div>
                    </div>
                </div>

                <div id="tableContainer" class="table-container">
                    <table role="grid">
                        <thead>
                            <tr>
                                <th scope="col">Edit</th>
                                <th scope="col">Company</th>
                                <th scope="col">Supersession Type <i class="fas fa-sort" aria-label="Sort"></i></th>
                                <th scope="col">Consumption Code</th>
                                <th scope="col">Replacing Code</th>
                                <th scope="col">Remarks</th>
                                <th scope="col">Active</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr data-index="0">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>None</td>
                                <td>one to one</td>
                                <td>1-1</td>
                                <td>Yes</td>
                            </tr>
                            <tr data-index="1">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>Pre-production</td>
                                <td>one to one</td>
                                <td>Super1</td>
                                <td>Yes</td>
                            </tr>
                                                     <tr data-index="0">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>None</td>
                                <td>one to one</td>
                                <td>1-1</td>
                                <td>Yes</td>
                            </tr>
                            <tr data-index="1">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>Pre-production</td>
                                <td>one to one</td>
                                <td>Super1</td>
                                <td>Yes</td>
                            </tr>
                                                     <tr data-index="0">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>None</td>
                                <td>one to one</td>
                                <td>1-1</td>
                                <td>Yes</td>
                            </tr>
                            <tr data-index="1">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>Pre-production</td>
                                <td>one to one</td>
                                <td>Super1</td>
                                <td>Yes</td>
                            </tr>
                                                     <tr data-index="0">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>None</td>
                                <td>one to one</td>
                                <td>1-1</td>
                                <td>Yes</td>
                            </tr>
                            <tr data-index="1">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>Pre-production</td>
                                <td>one to one</td>
                                <td>Super1</td>
                                <td>Yes</td>
                            </tr>
                                                     <tr data-index="0">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>None</td>
                                <td>one to one</td>
                                <td>1-1</td>
                                <td>Yes</td>
                            </tr>
                            <tr data-index="1">
                                <td><i class="fas fa-edit" aria-label="Edit"></i></td>
                                <td>HCL AMP</td>
                                <td>One to One</td>
                                <td>Pre-production</td>
                                <td>one to one</td>
                                <td>Super1</td>
                                <td>Yes</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div id="cardContainer" class="card-container" style="display: none;">
                    <div class="card" data-index="0">
                        <div class="card-header">
                            <span class="card-title">HCL AMP</span>
                            <i class="fas fa-edit" aria-label="Edit"></i>
                        </div>
                        <div class="card-body">
                            <p><strong>Type:</strong> One to One</p>
                            <p><strong>Consumption:</strong> None</p>
                            <p><strong>Replacing:</strong> one to one</p>
                            <p><strong>Remarks:</strong> 1-1</p>
                            <p><strong>Active:</strong> Yes</p>
                        </div>
                    </div>
                    <div class="card" data-index="1">
                        <div class="card-header">
                            <span class="card-title">HCL AMP</span>
                            <i class="fas fa-edit" aria-label="Edit"></i>
                        </div>
                        <div class="card-body">
                            <p><strong>Type:</strong> One to One</p>
                            <p><strong>Consumption:</strong> Pre-production</p>
                            <p><strong>Replacing:</strong> one to one</p>
                            <p><strong>Remarks:</strong> Super1</p>
                            <p><strong>Active:</strong> Yes</p>
                        </div>
                    </div>
                </div>
            </section>

            <div class="pagination">
                <button class="btn btn-secondary" aria-label="First Page"><i class="fas fa-angle-double-left"></i></button>
                <button class="btn btn-secondary" aria-label="Previous Page"><i class="fas fa-angle-left"></i></button>
                <span>Page</span>
                <input type="number" value="1" min="1" aria-label="Page Number">
                <span>of 1</span>
                <select aria-label="Items per page">
                    <option value="10">10</option>
                    <option value="20">20</option>
                </select>
                <button class="btn btn-secondary" aria-label="Next Page"><i class="fas fa-angle-right"></i></button>
                <button class="btn btn-secondary" aria-label="Last Page"><i class="fas fa-angle-double-right"></i></button>
                <span class="view-info">1 - 2 of 2</span>
            </div>

            <!-- Edit Modal -->
            <div id="editModal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Edit Supersession</h2>
                        <button class="modal-close" aria-label="Close Modal">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-left">
                            <form id="editForm">
                                <input type="hidden" id="editIndex">
                                <label for="supersessionType">Supersession Type</label>
                                <input type="text" id="supersessionType" name="supersessionType" disabled required>
                                <label for="consumptionCode">Consumption Code</label>
                                <input type="text" id="consumptionCode" name="consumptionCode" disabled required>
                                <label for="replacingCode">Replacing Code</label>
                                <input type="text" id="replacingCode" name="replacingCode" disabled required>
                                <label for="remarks">Remarks</label>
                                <input type="text" id="remarks" name="remarks" disabled required>
                                <label for="active">Active</label>
                                <select id="active" name="active" disabled required>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                </select>
                            </form>
                        </div>
                        <div class="modal-right">
                            <!-- Parts Details Accordion -->
                            <div class="accordion">
                                <div class="accordion-header" id="partsAccordionHeader">
                                    <h3>Parts Details</h3>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content" id="partsAccordionContent">
                                    <div class="parts-details-toggle">
                                        <button class="btn active" id="partsTableViewBtn" aria-label="Table View">Table View</button>
                                        <button class="btn" id="partsCardViewBtn" aria-label="Card View">Card View</button>
                                    </div>
                                    <div class="parts-details-table active" id="partsTableView">
                                        <div class="parts-details-table-container">
                                            <table role="grid">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">Prefix</th>
                                                        <th scope="col">Part#</th>
                                                        <th scope="col">Description</th>
                                                        <th scope="col">Qty</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>ENG</td>
                                                        <td>XYZ123</td>
                                                        <td>Engine Component</td>
                                                        <td>5</td>
                                                    </tr>
                                                    <tr>
                                                        <td>BRK</td>
                                                        <td>ABC456</td>
                                                        <td>Brake Pad</td>
                                                        <td>10</td>
                                                    </tr>
                                                    <tr>
                                                        <td>FLT</td>
                                                        <td>DEF789</td>
                                                        <td>Air Filter</td>
                                                        <td>8</td>
                                                    </tr>
                                                    <tr>
                                                        <td>IGN</td>
                                                        <td>GHI101</td>
                                                        <td>Ignition Coil</td>
                                                        <td>3</td>
                                                    </tr>
                                                    <tr>
                                                        <td>SUS</td>
                                                        <td>JKL202</td>
                                                        <td>Suspension Spring</td>
                                                        <td>6</td>
                                                    </tr>
                                                    <tr>
                                                        <td>EXH</td>
                                                        <td>MNO303</td>
                                                        <td>Exhaust Pipe</td>
                                                        <td>4</td>
                                                    </tr>
                                                    <tr>
                                                        <td>CLT</td>
                                                        <td>PQR404</td>
                                                        <td>Clutch Plate</td>
                                                        <td>7</td>
                                                    </tr>
                                                    <tr>
                                                        <td>RAD</td>
                                                        <td>STU505</td>
                                                        <td>Radiator</td>
                                                        <td>2</td>
                                                    </tr>
                                                    <tr>
                                                        <td>FUL</td>
                                                        <td>VWX606</td>
                                                        <td>Fuel Pump</td>
                                                        <td>9</td>
                                                    </tr>
                                                    <tr>
                                                        <td>TRN</td>
                                                        <td>YZA707</td>
                                                        <td>Transmission Gear</td>
                                                        <td>5</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="parts-details-card" id="partsCardView">
                                        <div class="parts-details-card-container">
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> ENG</p>
                                                <p><strong>Part#:</strong> XYZ123</p>
                                                <p><strong>Description:</strong> Engine Component</p>
                                                <p><strong>Qty:</strong> 5</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> BRK</p>
                                                <p><strong>Part#:</strong> ABC456</p>
                                                <p><strong>Description:</strong> Brake Pad</p>
                                                <p><strong>Qty:</strong> 10</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> FLT</p>
                                                <p><strong>Part#:</strong> DEF789</p>
                                                <p><strong>Description:</strong> Air Filter</p>
                                                <p><strong>Qty:</strong> 8</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> IGN</p>
                                                <p><strong>Part#:</strong> GHI101</p>
                                                <p><strong>Description:</strong> Ignition Coil</p>
                                                <p><strong>Qty:</strong> 3</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> SUS</p>
                                                <p><strong>Part#:</strong> JKL202</p>
                                                <p><strong>Description:</strong> Suspension Spring</p>
                                                <p><strong>Qty:</strong> 6</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> EXH</p>
                                                <p><strong>Part#:</strong> MNO303</p>
                                                <p><strong>Description:</strong> Exhaust Pipe</p>
                                                <p><strong>Qty:</strong> 4</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> CLT</p>
                                                <p><strong>Part#:</strong> PQR404</p>
                                                <p><strong>Description:</strong> Clutch Plate</p>
                                                <p><strong>Qty:</strong> 7</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> RAD</p>
                                                <p><strong>Part#:</strong> STU505</p>
                                                <p><strong>Description:</strong> Radiator</p>
                                                <p><strong>Qty:</strong> 2</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> FUL</p>
                                                <p><strong>Part#:</strong> VWX606</p>
                                                <p><strong>Description:</strong> Fuel Pump</p>
                                                <p><strong>Qty:</strong> 9</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> TRN</p>
                                                <p><strong>Part#:</strong> YZA707</p>
                                                <p><strong>Description:</strong> Transmission Gear</p>
                                                <p><strong>Qty:</strong> 5</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Superseding Parts Details Accordion -->
                            <div class="accordion">
                                <div class="accordion-header" id="supersedingAccordionHeader">
                                    <h3>Superseding Parts Details</h3>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content" id="supersedingAccordionContent">
                                    <div class="superseding-parts-toggle">
                                        <button class="btn active" id="supersedingTableViewBtn" aria-label="Table View">Table View</button>
                                        <button class="btn" id="supersedingCardViewBtn" aria-label="Card View">Card View</button>
                                    </div>
                                    <div class="superseding-parts-table active" id="supersedingTableView">
                                        <div class="superseding-parts-table-container">
                                            <table role="grid">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">Prefix</th>
                                                        <th scope="col">Part#</th>
                                                        <th scope="col">Description</th>
                                                        <th scope="col">Qty</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>ENG</td>
                                                        <td>XYZ123</td>
                                                        <td>Engine Component</td>
                                                        <td>5</td>
                                                    </tr>
                                                    <tr>
                                                        <td>BRK</td>
                                                        <td>ABC456</td>
                                                        <td>Brake Pad</td>
                                                        <td>10</td>
                                                    </tr>
                                                    <tr>
                                                        <td>FLT</td>
                                                        <td>DEF789</td>
                                                        <td>Air Filter</td>
                                                        <td>8</td>
                                                    </tr>
                                                    <tr>
                                                        <td>IGN</td>
                                                        <td>GHI101</td>
                                                        <td>Ignition Coil</td>
                                                        <td>3</td>
                                                    </tr>
                                                    <tr>
                                                        <td>SUS</td>
                                                        <td>JKL202</td>
                                                        <td>Suspension Spring</td>
                                                        <td>6</td>
                                                    </tr>
                                                    <tr>
                                                        <td>EXH</td>
                                                        <td>MNO303</td>
                                                        <td>Exhaust Pipe</td>
                                                        <td>4</td>
                                                    </tr>
                                                    <tr>
                                                        <td>CLT</td>
                                                        <td>PQR404</td>
                                                        <td>Clutch Plate</td>
                                                        <td>7</td>
                                                    </tr>
                                                    <tr>
                                                        <td>RAD</td>
                                                        <td>STU505</td>
                                                        <td>Radiator</td>
                                                        <td>2</td>
                                                    </tr>
                                                    <tr>
                                                        <td>FUL</td>
                                                        <td>VWX606</td>
                                                        <td>Fuel Pump</td>
                                                        <td>9</td>
                                                    </tr>
                                                    <tr>
                                                        <td>TRN</td>
                                                        <td>YZA707</td>
                                                        <td>Transmission Gear</td>
                                                        <td>5</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="superseding-parts-card" id="supersedingCardView">
                                        <div class="superseding-parts-card-container">
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> ENG</p>
                                                <p><strong>Part#:</strong> XYZ123</p>
                                                <p><strong>Description:</strong> Engine Component</p>
                                                <p><strong>Qty:</strong> 5</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> BRK</p>
                                                <p><strong>Part#:</strong> ABC456</p>
                                                <p><strong>Description:</strong> Brake Pad</p>
                                                <p><strong>Qty:</strong> 10</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> FLT</p>
                                                <p><strong>Part#:</strong> DEF789</p>
                                                <p><strong>Description:</strong> Air Filter</p>
                                                <p><strong>Qty:</strong> 8</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> IGN</p>
                                                <p><strong>Part#:</strong> GHI101</p>
                                                <p><strong>Description:</strong> Ignition Coil</p>
                                                <p><strong>Qty:</strong> 3</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> SUS</p>
                                                <p><strong>Part#:</strong> JKL202</p>
                                                <p><strong>Description:</strong> Suspension Spring</p>
                                                <p><strong>Qty:</strong> 6</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> EXH</p>
                                                <p><strong>Part#:</strong> MNO303</p>
                                                <p><strong>Description:</strong> Exhaust Pipe</p>
                                                <p><strong>Qty:</strong> 4</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> CLT</p>
                                                <p><strong>Part#:</strong> PQR404</p>
                                                <p><strong>Description:</strong> Clutch Plate</p>
                                                <p><strong>Qty:</strong> 7</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> RAD</p>
                                                <p><strong>Part#:</strong> STU505</p>
                                                <p><strong>Description:</strong> Radiator</p>
                                                <p><strong>Qty:</strong> 2</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> FUL</p>
                                                <p><strong>Part#:</strong> VWX606</p>
                                                <p><strong>Description:</strong> Fuel Pump</p>
                                                <p><strong>Qty:</strong> 9</p>
                                            </div>
                                            <div style="margin-bottom: 16px;">
                                                <p><strong>Prefix:</strong> TRN</p>
                                                <p><strong>Part#:</strong> YZA707</p>
                                                <p><strong>Description:</strong> Transmission Gear</p>
                                                <p><strong>Qty:</strong> 5</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- History Accordion -->
                            <div class="accordion">
                                <div class="accordion-header" id="historyAccordionHeader">
                                    <h3>History</h3>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="accordion-content" id="historyAccordionContent">
                                    <div class="history-table-container">
                                        <table role="grid">
                                            <thead>
                                                <tr>
                                                    <th scope="col">Date Modified</th>
                                                    <th scope="col">Replacing Code</th>
                                                    <th scope="col">Remarks</th>
                                                </tr>
                                            </thead>
                                            <tbody id="historyTable">
                                                <tr>
                                                    <td>2025-05-27</td>
                                                    <td>one to one</td>
                                                    <td>Initial</td>
                                                </tr>
                                                <tr>
                                                    <td>2025-05-28</td>
                                                    <td>one to one v2</td>
                                                    <td>Updated</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-cancel" id="cancelBtn" aria-label="Cancel">Cancel</button>
                        <button class="btn" id="editBtn" aria-label="Edit">Edit</button>
                        <button class="btn" id="saveBtn" style="display: none;" aria-label="Save Changes">Save</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Sidebar toggle
        const sidebar = document.querySelector('.sidebar');
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('expanded');
        });

        // Top nav toggle (mobile)
        const topNav = document.querySelector('.top-nav');
        const topNavToggle = document.querySelector('.top-nav-toggle');
        topNavToggle.addEventListener('click', () => {
            topNav.classList.toggle('expanded');
        });

        // View toggling with animation
        const tableViewBtn = document.getElementById('tableViewBtn');
        const cardViewBtn = document.getElementById('cardViewBtn');
        const tableContainer = document.getElementById('tableContainer');
        const cardContainer = document.getElementById('cardContainer');

        function setActiveView(view) {
            if (view === 'table') {
                cardContainer.classList.add('hidden');
                setTimeout(() => {
                    cardContainer.style.display = 'none';
                    tableContainer.style.display = 'block';
                    setTimeout(() => tableContainer.classList.remove('hidden'), 50);
                }, 400);
                tableViewBtn.classList.add('active');
                cardViewBtn.classList.remove('active');
                localStorage.setItem('currentView', 'table');
            } else {
                tableContainer.classList.add('hidden');
                setTimeout(() => {
                    tableContainer.style.display = 'none';
                    cardContainer.style.display = 'grid';
                    setTimeout(() => cardContainer.classList.remove('hidden'), 50);
                }, 400);
                tableViewBtn.classList.remove('active');
                cardViewBtn.classList.add('active');
                localStorage.setItem('currentView', 'cards');
            }
        }

        tableViewBtn.addEventListener('click', () => setActiveView('table'));
        cardViewBtn.addEventListener('click', () => setActiveView('cards'));

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        darkModeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-mode');
            const icon = darkModeToggle.querySelector('i');
            icon.classList.toggle('fa-moon');
            icon.classList.toggle('fa-sun');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // Accordion functionality for Parts Details
        const partsAccordionHeader = document.getElementById('partsAccordionHeader');
        const partsAccordionContent = document.getElementById('partsAccordionContent');

        partsAccordionHeader.addEventListener('click', () => {
            partsAccordionHeader.classList.toggle('active');
            partsAccordionContent.classList.toggle('active');
        });

        // Accordion functionality for Superseding Parts Details
        const supersedingAccordionHeader = document.getElementById('supersedingAccordionHeader');
        const supersedingAccordionContent = document.getElementById('supersedingAccordionContent');

        supersedingAccordionHeader.addEventListener('click', () => {
            supersedingAccordionHeader.classList.toggle('active');
            supersedingAccordionContent.classList.toggle('active');
        });

        // Accordion functionality for History
        const historyAccordionHeader = document.getElementById('historyAccordionHeader');
        const historyAccordionContent = document.getElementById('historyAccordionContent');

        historyAccordionHeader.addEventListener('click', () => {
            historyAccordionHeader.classList.toggle('active');
            historyAccordionContent.classList.toggle('active');
        });

        // Parts Details view toggle
        const partsTableViewBtn = document.getElementById('partsTableViewBtn');
        const partsCardViewBtn = document.getElementById('partsCardViewBtn');
        const partsTableView = document.getElementById('partsTableView');
        const partsCardView = document.getElementById('partsCardView');

        partsTableViewBtn.addEventListener('click', () => {
            partsTableView.classList.add('active');
            partsCardView.classList.remove('active');
            partsTableViewBtn.classList.add('active');
            partsCardViewBtn.classList.remove('active');
        });

        partsCardViewBtn.addEventListener('click', () => {
            partsCardView.classList.add('active');
            partsTableView.classList.remove('active');
            partsCardViewBtn.classList.add('active');
            partsTableViewBtn.classList.remove('active');
        });

        // Superseding Parts Details view toggle
        const supersedingTableViewBtn = document.getElementById('supersedingTableViewBtn');
        const supersedingCardViewBtn = document.getElementById('supersedingCardViewBtn');
        const supersedingTableView = document.getElementById('supersedingTableView');
        const supersedingCardView = document.getElementById('supersedingCardView');

        supersedingTableViewBtn.addEventListener('click', () => {
            supersedingTableView.classList.add('active');
            supersedingCardView.classList.remove('active');
            supersedingTableViewBtn.classList.add('active');
            supersedingCardViewBtn.classList.remove('active');
        });

        supersedingCardViewBtn.addEventListener('click', () => {
            supersedingCardView.classList.add('active');
            supersedingTableView.classList.remove('active');
            supersedingCardViewBtn.classList.add('active');
            supersedingTableViewBtn.classList.remove('active');
        });

        // Edit modal functionality
        const editModal = document.getElementById('editModal');
        const editForm = document.getElementById('editForm');
        const editIndexInput = document.getElementById('editIndex');
        const supersessionTypeInput = document.getElementById('supersessionType');
        const consumptionCodeInput = document.getElementById('consumptionCode');
        const replacingCodeInput = document.getElementById('replacingCode');
        const remarksInput = document.getElementById('remarks');
        const activeInput = document.getElementById('active');
        const editBtn = document.getElementById('editBtn');
        const saveBtn = document.getElementById('saveBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const closeBtn = document.querySelector('.modal-close');
        const historyTable = document.getElementById('historyTable');

        // Open modal when edit icon is clicked
        document.querySelectorAll('.fa-edit').forEach(icon => {
            icon.addEventListener('click', (e) => {
                const parent = icon.closest('tr') || icon.closest('.card');
                const index = parent.dataset.index;
                let supersessionType, consumptionCode, replacingCode, remarks, active;

                if (parent.tagName === 'TR') {
                    // Table view
                    const cells = parent.querySelectorAll('td');
                    supersessionType = cells[2].textContent;
                    consumptionCode = cells[3].textContent;
                    replacingCode = cells[4].textContent;
                    remarks = cells[5].textContent;
                    active = cells[6].textContent;
                } else {
                    // Card view
                    const body = parent.querySelector('.card-body');
                    supersessionType = body.querySelector('p:nth-child(1)').textContent.replace('Type: ', '');
                    consumptionCode = body.querySelector('p:nth-child(2)').textContent.replace('Consumption: ', '');
                    replacingCode = body.querySelector('p:nth-child(3)').textContent.replace('Replacing: ', '');
                    remarks = body.querySelector('p:nth-child(4)').textContent.replace('Remarks: ', '');
                    active = body.querySelector('p:nth-child(5)').textContent.replace('Active: ', '');
                }

                // Populate modal form
                editIndexInput.value = index;
                supersessionTypeInput.value = supersessionType;
                consumptionCodeInput.value = consumptionCode;
                replacingCodeInput.value = replacingCode;
                remarksInput.value = remarks;
                activeInput.value = active;

                // Reset button states
                replacingCodeInput.disabled = true;
                remarksInput.disabled = true;
                editBtn.style.display = 'block';
                saveBtn.style.display = 'none';

                // Reset accordion states
                partsAccordionHeader.classList.remove('active');
                partsAccordionContent.classList.remove('active');
                supersedingAccordionHeader.classList.remove('active');
                supersedingAccordionContent.classList.remove('active');
                historyAccordionHeader.classList.remove('active');
                historyAccordionContent.classList.remove('active');

                editModal.style.display = 'flex';
            });
        });

        // Enable editing when Edit button is clicked
        editBtn.addEventListener('click', () => {
            replacingCodeInput.disabled = false;
            remarksInput.disabled = false;
            editBtn.style.display = 'none';
            saveBtn.style.display = 'block';
        });

        // Close modal
        function closeModal() {
            editModal.style.display = 'none';
            editForm.reset();
            replacingCodeInput.disabled = true;
            remarksInput.disabled = true;
            editBtn.style.display = 'block';
            saveBtn.style.display = 'none';
            partsAccordionHeader.classList.remove('active');
            partsAccordionContent.classList.remove('active');
            supersedingAccordionHeader.classList.remove('active');
            supersedingAccordionContent.classList.remove('active');
            historyAccordionHeader.classList.remove('active');
            historyAccordionContent.classList.remove('active');
        }

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // Close modal when clicking outside
        editModal.addEventListener('click', (e) => {
            if (e.target === editModal) {
                closeModal();
            }
        });

        // Save changes
        saveBtn.addEventListener('click', (e) => {
            e.preventDefault();
            const index = editIndexInput.value;
            const replacingCode = replacingCodeInput.value;
            const remarks = remarksInput.value;

            // Update table
            const tableRow = document.querySelector(`#tableContainer tr[data-index="${index}"]`);
            if (tableRow) {
                const cells = tableRow.querySelectorAll('td');
                cells[4].textContent = replacingCode;
                cells[5].textContent = remarks;
            }

            // Update card
            const card = document.querySelector(`#cardContainer .card[data-index="${index}"]`);
            if (card) {
                const body = card.querySelector('.card-body');
                body.querySelector('p:nth-child(3)').textContent = `Replacing: ${replacingCode}`;
                body.querySelector('p:nth-child(4)').textContent = `Remarks: ${remarks}`;
            }

            // Add to history table (simulated)
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>${new Date().toISOString().split('T')[0]}</td>
                <td>${replacingCode}</td>
                <td>${remarks}</td>
            `;
            historyTable.insertBefore(newRow, historyTable.firstChild);

            closeModal();
        });

        // Load saved preferences
        document.addEventListener('DOMContentLoaded', () => {
            const savedView = localStorage.getItem('currentView') || 'table';
            setActiveView(savedView);
            if (localStorage.getItem('darkMode') === 'true') {
                document.body.classList.add('dark-mode');
                darkModeToggle.querySelector('i').classList.replace('fa-moon', 'fa-sun');
            }
        });
    </script>
</body>
</html>