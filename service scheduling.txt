<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Scheduling - Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        /* --- Theme Variables (Black & White with Greys) --- */
        :root {
            --font-family-main: '<PERSON><PERSON>', sans-serif;
            --text-primary: #212121;
            --text-secondary: #757575;
            --text-on-dark: #FFFFFF;
            --text-on-accent: #FFFFFF;
            --bg-body: #F5F5F5;
            --bg-sidebar: #212121;
            --bg-card: #FFFFFF;
            --bg-app-bar: #FFFFFF;
            --bg-modal-backdrop: rgba(0, 0, 0, 0.5);
            --border-color: #E0E0E0;
            --divider-color: #BDBDBD;
            --accent-color: #000000;
            --accent-color-hover: #333333;
            --shadow-umbra1: 0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12);
            --shadow-umbra2: 0px 3px 3px -2px rgba(0,0,0,0.2), 0px 3px 4px 0px rgba(0,0,0,0.14), 0px 1px 8px 0px rgba(0,0,0,0.12);
            --shadow-dialog: 0px 11px 15px -7px rgba(0,0,0,0.2), 0px 24px 38px 3px rgba(0,0,0,0.14), 0px 9px 46px 8px rgba(0,0,0,0.12);
        }

        /* --- Basic Reset & Body --- */
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body {
            font-family: var(--font-family-main);
            background-color: var(--bg-body);
            color: var(--text-primary);
            line-height: 1.6;
            display: flex;
            min-height: 100vh;
        }
        .app-container {
            display: flex;
            width: 100vw;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* --- Sidebar --- */
        .sidebar {
            width: 260px;
            background-color: var(--bg-sidebar);
            color: var(--text-on-dark);
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            box-shadow: var(--shadow-umbra2);
            z-index: 1000; /* Ensure sidebar is above modal backdrop */
            position: fixed;
            left: 0; top: 0; bottom: 0;
            overflow-y: auto;
        }
        .sidebar-header { padding: 20px; text-align: center; border-bottom: 1px solid #424242; }
        .sidebar-header h2 { font-weight: 500; color: var(--text-on-dark); }
        .nav-list { list-style: none; flex-grow: 1; padding-top: 10px; }
        .nav-item {
            display: flex; align-items: center; padding: 12px 20px; cursor: pointer;
            transition: background-color 0.2s ease; font-weight: 400; color: var(--text-on-dark);
        }
        .nav-item:hover { background-color: rgba(255, 255, 255, 0.1); }
        .nav-item.active {
            background-color: rgba(255, 255, 255, 0.2); font-weight: 500;
            border-left: 3px solid var(--text-on-dark); padding-left: 17px;
        }
        .nav-item .material-icons { margin-right: 20px; font-size: 22px; opacity: 0.8; color: var(--text-on-dark); }
        .nav-item.active .material-icons { opacity: 1; }
        .sidebar-footer { padding: 15px; border-top: 1px solid #424242; margin-top: auto; }
        .user-profile-brief { display: flex; align-items: center; padding: 10px 5px; font-size: 0.9em; margin-bottom: 10px; color: var(--text-on-dark); }
        .user-profile-brief .material-icons { margin-right: 10px; color: var(--text-on-dark); }
        .logout-button {
            background-color: transparent; color: var(--text-on-dark); border: 1px solid var(--text-on-dark);
            padding: 8px 12px; width: 100%; text-align: left; cursor: pointer; border-radius: 4px;
            display: flex; align-items: center; font-size: 0.9em;
        }
        .logout-button:hover { background-color: rgba(255, 255, 255, 0.1); }
        .logout-button .material-icons { margin-right: 8px; font-size: 18px; color: var(--text-on-dark); }

        /* --- Main Content Area --- */
        .main-content {
            flex-grow: 1; overflow-y: auto; background-color: var(--bg-body);
            display: flex; flex-direction: column; margin-left: 260px;
            width: calc(100% - 260px); min-height: 100vh;
        }
        .app-bar {
            background-color: var(--bg-app-bar); color: var(--text-primary); padding: 0 24px;
            height: 64px; display: flex; align-items: center; justify-content: space-between;
            box-shadow: var(--shadow-umbra1); position: sticky; top: 0; z-index: 900; width: 100%;
        }
        .app-bar-title h1 { font-size: 20px; font-weight: 500; }
        .app-bar-actions .material-icons { margin-left: 20px; cursor: pointer; color: var(--text-secondary); }
        .app-bar-actions .material-icons:hover { color: var(--text-primary); }

        /* --- Page Sections --- */
        .page-section { display: none; padding: 24px; flex-grow: 1; }
        .page-section.active { display: block; }
        .page-section h2 { font-size: 24px; font-weight: 400; margin-bottom: 20px; color: var(--text-primary); }

        /* --- Dashboard Grid & Cards (from previous) --- */
        .dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }
        .card {
            background-color: var(--bg-card); border-radius: 8px; box-shadow: var(--shadow-umbra1);
            transition: box-shadow 0.3s ease; display: flex; flex-direction: column;
        }
        .card:hover { box-shadow: var(--shadow-umbra2); }
        .card-header { padding: 16px 20px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; }
        .card-header h3 { font-size: 16px; font-weight: 500; color: var(--text-primary); }
        .card-icon { color: var(--text-secondary); }
        .card-content { padding: 20px; flex-grow: 1; }
        .metric { font-size: 2.5em; font-weight: 300; color: var(--text-primary); margin-bottom: 4px; }
        .sub-text { font-size: 0.9em; color: var(--text-secondary); }
        .progress-bar-container { height: 8px; background-color: #E0E0E0; border-radius: 4px; overflow: hidden; margin: 10px 0; }
        .progress-bar { height: 100%; background-color: var(--accent-color); border-radius: 4px; }
        .button {
            padding: 8px 16px; border-radius: 4px; cursor: pointer; font-family: var(--font-family-main);
            font-size: 14px; font-weight: 500; text-transform: uppercase; border: none;
            transition: background-color 0.2s ease, box-shadow 0.2s ease; display: inline-flex;
            align-items: center; justify-content: center; margin-right: 8px; margin-bottom: 8px;
        }
        .button .material-icons { margin-right: 8px; font-size: 18px; }
        .button-contained { background-color: var(--accent-color); color: var(--text-on-accent); box-shadow: var(--shadow-umbra1); }
        .button-contained:hover { background-color: var(--accent-color-hover); box-shadow: var(--shadow-umbra2); }
        .button-outlined { background-color: transparent; color: var(--accent-color); border: 1px solid var(--accent-color); }
        .button-outlined:hover { background-color: rgba(0, 0, 0, 0.04); }
        .button-text { background-color: transparent; color: var(--accent-color); padding-left: 8px; padding-right: 8px; }
        .button-text:hover { background-color: rgba(0, 0, 0, 0.04); }
        .card-table table { width: 100%; border-collapse: collapse; }
        .card-table th, .card-table td { text-align: left; padding: 10px 8px; border-bottom: 1px solid var(--border-color); font-size: 0.9em; }
        .card-table th { font-weight: 500; color: var(--text-secondary); }
        .card-table tbody tr:last-child td { border-bottom: none; }
        .activity-list { list-style: none; padding-left: 0; }
        .activity-list li { font-size: 0.9em; color: var(--text-secondary); margin-bottom: 8px; border-bottom: 1px dashed var(--border-color); padding-bottom: 8px; }
        .activity-list li:last-child { border-bottom: none; margin-bottom: 0; padding-bottom: 0;}
        .activity-time { font-weight: 500; color: var(--text-primary); display: block; margin-bottom: 2px; font-size: 0.8em;}
        .alerts-list { list-style: none; padding-left: 0; }
        .alerts-list li { font-size: 0.9em; color: var(--text-secondary); margin-bottom: 8px; padding: 8px; border-radius: 4px; }
        .alerts-list li.alert-critical { background-color: #FFEBEE; color: #C62828; border-left: 3px solid #C62828; }
        .alerts-list li.alert-warning { background-color: #FFF8E1; color: #FF8F00; border-left: 3px solid #FF8F00; }

        /* --- Modal Styles --- */
        .modal-backdrop {
            display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: var(--bg-modal-backdrop); z-index: 1040; align-items: center; justify-content: center;
        }
        .modal-backdrop.active { display: flex; }
        .modal-dialog {
            background-color: var(--bg-card); border-radius: 8px; box-shadow: var(--shadow-dialog);
            width: 90%; max-width: 500px; display: flex; flex-direction: column;
            max-height: 90vh;
        }
        .modal-header {
            padding: 16px 24px; border-bottom: 1px solid var(--border-color);
            display: flex; justify-content: space-between; align-items: center;
        }
        .modal-header h3 { font-size: 18px; font-weight: 500; color: var(--text-primary); }
        .modal-close-button { background: none; border: none; font-size: 24px; cursor: pointer; color: var(--text-secondary); }
        .modal-close-button:hover { color: var(--text-primary); }
        .modal-body { padding: 24px; overflow-y: auto; }
        .modal-footer { padding: 16px 24px; border-top: 1px solid var(--border-color); text-align: right; }

        /* Form Styles (Basic) */
        .form-group { margin-bottom: 16px; }
        .form-group label { display: block; font-size: 0.9em; color: var(--text-secondary); margin-bottom: 6px; font-weight: 500; }
        .form-group input[type="text"],
        .form-group input[type="email"],
        .form-group input[type="tel"],
        .form-group input[type="password"],
        .form-group input[type="datetime-local"],
        .form-group input[type="number"],
        .form-group select,
        .form-group textarea {
            width: 100%; padding: 10px; border: 1px solid var(--border-color);
            border-radius: 4px; font-size: 1em; font-family: var(--font-family-main);
        }
        .form-group textarea { min-height: 80px; resize: vertical; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none; border-color: var(--accent-color); box-shadow: 0 0 0 2px rgba(0,0,0,0.1);
        }

        /* Placeholder Content Styles */
        .placeholder-content {
            border: 2px dashed var(--border-color); padding: 40px; text-align: center;
            color: var(--text-secondary); border-radius: 8px; margin-top: 20px;
        }
        .placeholder-content .material-icons { font-size: 48px; margin-bottom: 16px; }

        /* Mobile Adjustments */
        @media (max-width: 768px) {
            .sidebar { width: 100%; height: auto; position: relative; box-shadow: none; z-index: 100; }
            .main-content { margin-left: 0; width: 100%; }
            .app-container { flex-direction: column; } /* Stack sidebar and main content */
            .app-bar { padding: 0 16px; height: 56px; }
            .app-bar-title h1 { font-size: 18px; }
            .app-bar-actions .material-icons { margin-left: 12px; }
            .dashboard-grid { grid-template-columns: 1fr; padding: 16px; }
            .page-section { padding: 16px; }
            .button { padding: 10px 12px; font-size: 13px; }
            .modal-dialog { width: 95%; }

            /* Basic Mobile Menu Toggle (conceptual, needs JS to fully implement hide/show) */
            .sidebar-header .mobile-menu-toggle { display: block; position: absolute; right: 10px; top: 15px; cursor: pointer;}
            /* .nav-list { display: none; } */ /* Initially hide for toggle */
            /* .nav-list.open { display: block; } */
        }
        .mobile-menu-toggle { display: none; } /* Hide on desktop */

    </style>
</head>
<body>
    <div class="app-container">
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>SaaS App</h2>
                </div>
            <ul class="nav-list">
                <li class="nav-item active" data-page="dashboard">
                    <i class="material-icons">dashboard</i><span>Dashboard</span>
                </li>
                <li class="nav-item" data-page="calendar">
                    <i class="material-icons">calendar_today</i><span>Calendar View</span>
                </li>
                <li class="nav-item" data-page="appointments">
                    <i class="material-icons">list_alt</i><span>Appointments</span>
                </li>
                <li class="nav-item" data-page="clients">
                    <i class="material-icons">people</i><span>Clients</span>
                </li>
                <li class="nav-item" data-page="providers">
                    <i class="material-icons">badge</i><span>Service Providers</span>
                </li>
                <li class="nav-item" data-page="services">
                    <i class="material-icons">design_services</i><span>Services</span>
                </li>
                <li class="nav-item" data-page="reports">
                    <i class="material-icons">assessment</i><span>Reports</span>
                </li>
                <li class="nav-item" data-page="settings">
                    <i class="material-icons">settings</i><span>Settings</span>
                </li>
            </ul>
            <div class="sidebar-footer">
                <div class="user-profile-brief">
                    <i class="material-icons">account_circle</i><span>Admin User</span>
                </div>
                <button class="logout-button" id="logoutButton"><i class="material-icons">logout</i><span>Logout</span></button>
            </div>
        </nav>

        <main class="main-content">
            <header class="app-bar">
                <div class="app-bar-title"><h1 id="pageTitle">Admin Dashboard</h1></div>
                <div class="app-bar-actions">
                    <i class="material-icons">search</i>
                    <i class="material-icons">notifications</i>
                    <i class="material-icons">account_circle</i>
                </div>
            </header>

            <section id="dashboard" class="page-section active">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header"><h3>Total Appointments Today</h3><i class="material-icons card-icon">event_available</i></div>
                        <div class="card-content"><p class="metric">25</p><p class="sub-text">+3 from yesterday</p></div>
                    </div>
                    <div class="card">
                        <div class="card-header"><h3>Quick Actions</h3><i class="material-icons card-icon">bolt</i></div>
                        <div class="card-content">
                            <button class="button button-contained" id="openNewAppointmentModal"><i class="material-icons">add_circle_outline</i> New Appointment</button>
                            <button class="button button-outlined" id="openAddProviderModal"><i class="material-icons">group_add</i> Add Provider</button>
                            <button class="button button-text">View All Services</button>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header"><h3>Provider Utilization</h3><i class="material-icons card-icon">pie_chart</i></div>
                        <div class="card-content"><p class="metric">78%</p><div class="progress-bar-container"><div class="progress-bar" style="width: 78%;"></div></div><p class="sub-text">Across all active providers</p></div>
                    </div>
                    <div class="card card-table">
                        <div class="card-header"><h3>Popular Services</h3><i class="material-icons card-icon">category</i></div>
                        <div class="card-content">
                            <table><thead><tr><th>Service Name</th><th>Duration</th><th>Price</th></tr></thead>
                            <tbody>
                                <tr><td>Standard Maintenance</td><td>2 hrs</td><td>$75</td></tr>
                                <tr><td>Online Consultation</td><td>1 hr</td><td>$50</td></tr>
                                <tr><td>Premium Installation</td><td>4 hrs</td><td>$200</td></tr>
                                <tr><td>Basic Support Call</td><td>30 mins</td><td>$25</td></tr>
                                <tr><td>Full System Audit</td><td>8 hrs</td><td>$500</td></tr>
                            </tbody></table>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header"><h3>Recent Activity</h3><i class="material-icons card-icon">history</i></div>
                        <div class="card-content">
                            <ul class="activity-list">
                                <li><span class="activity-time">10:32 AM</span> Appointment #1203 confirmed.</li>
                                <li><span class="activity-time">09:15 AM</span> Provider "Jane Doe" updated availability.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header"><h3>System Alerts</h3><i class="material-icons card-icon" style="color: #D32F2F;">warning</i></div>
                        <div class="card-content">
                            <ul class="alerts-list">
                                <li class="alert-critical">Payment gateway experiencing issues.</li>
                                <li class="alert-warning">Upcoming server maintenance at 2 AM.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <section id="calendar" class="page-section">
                <h2>Calendar View</h2>
                <div class="placeholder-content">
                    <i class="material-icons">calendar_today</i>
                    <p>Full calendar integration (Google, Outlook, Apple) with two-way sync would be implemented here.</p>
                    <p>Features: View available slots, book new appointments, see existing schedules, handle time zones.</p>
                </div>
                </section>

            <section id="appointments" class="page-section">
                <h2>Appointments Management</h2>
                <div class="placeholder-content">
                    <i class="material-icons">list_alt</i>
                    <p>List, filter, and manage all appointments. Manual rescheduling and cancellation tools.</p>
                    <p>Automated notifications for confirmations, reminders, changes.</p>
                </div>
                 </section>

            <section id="clients" class="page-section">
                <h2>Client Management</h2>
                <div class="placeholder-content">
                    <i class="material-icons">people</i>
                    <p>Store and retrieve client information, contact details, appointment history.</p>
                    <p>Client self-scheduling portal access point.</p>
                </div>
                </section>

            <section id="providers" class="page-section">
                <h2>Service Provider Management</h2>
                <div class="placeholder-content">
                    <i class="material-icons">badge</i>
                    <p>Add, manage, and assign service providers. Define skills, availability, working hours, local time zones, and days off.</p>
                </div>
                 </section>

            <section id="services" class="page-section">
                <h2>Service Catalog Management</h2>
                <div class="placeholder-content">
                    <i class="material-icons">design_services</i>
                    <p>Define service types, duration, buffer times, required resources, pricing (multi-currency), custom booking forms.</p>
                    <p>Payment integration rules (pre-payment/deposits).</p>
                </div>
                </section>

            <section id="reports" class="page-section">
                <h2>Reporting & Analytics</h2>
                <div class="placeholder-content">
                    <i class="material-icons">assessment</i>
                    <p>Basic reports (volume, utilization, no-shows) and advanced analytics (custom dashboards, trends, forecasting).</p>
                </div>
                </section>

            <section id="settings" class="page-section">
                <h2>Settings</h2>
                <div class="placeholder-content">
                    <i class="material-icons">settings</i>
                    <p>User roles & permissions, notification template customization (localization), calendar integration settings, CRM/other system integrations, multi-location support, payment gateway configuration, localization & internationalization settings.</p>
                </div>
                </section>
        </main>
    </div>

    <div id="newAppointmentModal" class="modal-backdrop">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>New Appointment</h3>
                <button class="modal-close-button" data-close-modal="newAppointmentModal">&times;</button>
            </div>
            <div class="modal-body">
                <p style="margin-bottom: 10px;">M1. Core Appointment Booking</p>
                <form id="newAppointmentForm">
                    <div class="form-group">
                        <label for="clientName">Client Name (M8)</label>
                        <input type="text" id="clientName" name="clientName" required>
                    </div>
                    <div class="form-group">
                        <label for="serviceType">Service Type (M5)</label>
                        <select id="serviceType" name="serviceType" required>
                            <option value="">Select a service...</option>
                            <option value="consultation">Online Consultation (1hr)</option>
                            <option value="maintenance">Standard Maintenance (2hr)</option>
                            <option value="installation">Premium Installation (4hr)</option>
                        </select>
                    </div>
                     <div class="form-group">
                        <label for="appointmentDateTime">Date & Time (M1, M10 - User's Local Time)</label>
                        <input type="datetime-local" id="appointmentDateTime" name="appointmentDateTime" required>
                    </div>
                    <div class="form-group">
                        <label for="serviceProvider">Service Provider (M4, G10 - Skill-based if advanced)</label>
                        <select id="serviceProvider" name="serviceProvider" required>
                            <option value="">Any Available</option>
                            <option value="provider1">Dr. Smith (Cardiology)</option>
                            <option value="provider2">Tech Joe (Network)</option>
                        </select>
                    </div>
                    <div class="form-group"> <label for="customFieldIssue">Issue Description (Custom Field Example)</label>
                        <textarea id="customFieldIssue" name="customFieldIssue"></textarea>
                    </div>
                     <div class="form-group"> <label for="recurring">Recurring Appointment?</label>
                        <select id="recurring" name="recurring"><option value="no">No</option><option value="weekly">Weekly</option><option value="monthly">Monthly</option></select>
                    </div>
                    <p style="font-size:0.8em; color: var(--text-secondary); margin-top:10px;">Payment might be required for some services.</p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="button button-text" data-close-modal="newAppointmentModal">Cancel</button>
                <button type="submit" form="newAppointmentForm" class="button button-contained">Book Appointment</button>
            </div>
        </div>
    </div>

    <div id="addProviderModal" class="modal-backdrop">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>Add New Service Provider</h3>
                <button class="modal-close-button" data-close-modal="addProviderModal">&times;</button>
            </div>
            <div class="modal-body">
                <p style="margin-bottom: 10px;">M4. Service Provider Management</p>
                <form id="addProviderForm">
                    <div class="form-group">
                        <label for="providerName">Provider Name</label>
                        <input type="text" id="providerName" name="providerName" required>
                    </div>
                    <div class="form-group">
                        <label for="providerEmail">Email</label>
                        <input type="email" id="providerEmail" name="providerEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="providerSkills">Skills (comma separated, for G10)</label>
                        <input type="text" id="providerSkills" name="providerSkills">
                    </div>
                    <div class="form-group">
                        <label for="providerTimeZone">Working Time Zone (M4, M10)</label>
                        <select id="providerTimeZone" name="providerTimeZone"><option value="est">EST</option><option value="gmt">GMT</option><option value="pst">PST</option></select>
                    </div>
                    <div class="form-group">
                        <label for="providerWorkingHours">Working Hours (e.g., Mon-Fri 9am-5pm)</label>
                        <input type="text" id="providerWorkingHours" name="providerWorkingHours">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="button button-text" data-close-modal="addProviderModal">Cancel</button>
                <button type="submit" form="addProviderForm" class="button button-contained">Add Provider</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const navItems = document.querySelectorAll('.nav-item');
            const pageSections = document.querySelectorAll('.page-section');
            const pageTitle = document.getElementById('pageTitle');
            const logoutButton = document.getElementById('logoutButton');

            // --- Page Navigation ---
            function showPage(pageId, titleText) {
                pageSections.forEach(section => section.classList.remove('active'));
                navItems.forEach(item => item.classList.remove('active'));

                const activePage = document.getElementById(pageId);
                const activeNavItem = document.querySelector(`.nav-item[data-page="${pageId}"]`);

                if (activePage) activePage.classList.add('active');
                if (activeNavItem) activeNavItem.classList.add('active');
                if (pageTitle && titleText) pageTitle.textContent = titleText;
            }

            navItems.forEach(item => {
                item.addEventListener('click', () => {
                    const pageId = item.getAttribute('data-page');
                    const titleText = item.querySelector('span').textContent;
                    showPage(pageId, titleText);

                    // M11: Mobile Responsiveness - conceptual: close mobile menu if open
                    // const navList = document.querySelector('.nav-list');
                    // if(window.innerWidth <= 768 && navList.classList.contains('open')) {
                    //    navList.classList.remove('open');
                    // }
                });
            });

            // --- Modal Handling ---
            const openModalButtons = document.querySelectorAll('[data-modal-target]'); // Not used in current HTML, but good for future
            const closeModalButtons = document.querySelectorAll('[data-close-modal]');

            function openModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) modal.classList.add('active');
                // Full implementation would handle focus trapping and body scroll lock
            }

            function closeModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) modal.classList.remove('active');
            }

            // Specific modal openers
            const openNewAppointmentModalButton = document.getElementById('openNewAppointmentModal');
            if (openNewAppointmentModalButton) {
                openNewAppointmentModalButton.addEventListener('click', () => openModal('newAppointmentModal'));
            }

            const openAddProviderModalButton = document.getElementById('openAddProviderModal');
            if (openAddProviderModalButton) {
                openAddProviderModalButton.addEventListener('click', () => openModal('addProviderModal'));
            }

            closeModalButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const modalId = button.getAttribute('data-close-modal');
                    closeModal(modalId);
                });
            });

            // Close modal if backdrop is clicked
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.addEventListener('click', (event) => {
                    if (event.target === backdrop) {
                        closeModal(backdrop.id);
                    }
                });
            });

            // --- Form Submissions (Placeholder) ---
            const newAppointmentForm = document.getElementById('newAppointmentForm');
            if (newAppointmentForm) {
                newAppointmentForm.addEventListener('submit', (event) => {
                    event.preventDefault();
                    // M6: Automated Notifications & Reminders would be triggered here
                    alert('New Appointment form submitted (placeholder)! Data:\n' +
                          'Client: ' + event.target.clientName.value + '\n' +
                          'Service: ' + event.target.serviceType.value + '\n' +
                          'DateTime: ' + event.target.appointmentDateTime.value + '\n' +
                          'Provider: ' + event.target.serviceProvider.value + '\n' +
                          'Recurring: ' + event.target.recurring.value + '\n' +
                          'Issue: ' + event.target.customFieldIssue.value + '\n' +
                          'Further actions: Save data, check for conflicts (M2), send notifications (M6).');
                    closeModal('newAppointmentModal');
                    newAppointmentForm.reset();
                });
            }

            const addProviderForm = document.getElementById('addProviderForm');
            if (addProviderForm) {
                addProviderForm.addEventListener('submit', (event) => {
                    event.preventDefault();
                    alert('Add Provider form submitted (placeholder)! Data:\n' +
                          'Name: ' + event.target.providerName.value + '\n' +
                          'Email: ' + event.target.providerEmail.value + '\n' +
                          'Skills: ' + event.target.providerSkills.value + '\n' +
                          'Time Zone: ' + event.target.providerTimeZone.value + '\n' +
                          'Working Hours: ' + event.target.providerWorkingHours.value + '\n' +
                          'Further actions: Save provider data to system.');
                    closeModal('addProviderModal');
                    addProviderForm.reset();
                });
            }

            // --- Logout Button ---
            if(logoutButton) {
                logoutButton.addEventListener('click', () => {
                    alert('Logout clicked! (Placeholder - would typically redirect to login or clear session)');
                    // In a real app: clear session, redirect to login page, etc.
                });
            }

            // --- Initial Page Load ---
            showPage('dashboard', 'Admin Dashboard'); // Show dashboard by default

            // --- Placeholder for M11: Mobile menu toggle ---
            // const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            // const navList = document.querySelector('.nav-list');
            // if(mobileMenuToggle && navList) {
            //    mobileMenuToggle.addEventListener('click', () => {
            //        navList.classList.toggle('open');
            //    });
            // }

            console.log("Service Scheduling UI Initialized with basic interactivity.");
            console.log("NOTE: This is a front-end prototype. Full functionality for all listed features (M1-M11, G1-G12) requires significant backend development and integration with external services.");
        });
    </script>
</body>
</html>
