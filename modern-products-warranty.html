<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Products & Warranty System</title>

    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Black & White Theme Variables */
        :root {
            /* Light Theme */
            --primary-color: #000000;
            --primary-hover: #333333;
            --secondary-color: #666666;
            --background: #ffffff;
            --surface: #ffffff;
            --surface-elevated: #f8f8f8;
            --surface-hover: #f0f0f0;
            --text-primary: #000000;
            --text-secondary: #666666;
            --text-tertiary: #999999;
            --border-color: #e0e0e0;
            --border-dark: #cccccc;
            --success-color: #000000;
            --warning-color: #666666;
            --error-color: #000000;

            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-12: 3rem;

            /* Border Radius */
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

            /* Transitions */
            --transition: 0.2s ease-in-out;
            --transition-slow: 0.3s ease-in-out;
        }

        /* Dark Theme */
        [data-theme="dark"] {
            --primary-color: #ffffff;
            --primary-hover: #cccccc;
            --secondary-color: #999999;
            --background: #000000;
            --surface: #111111;
            --surface-elevated: #1a1a1a;
            --surface-hover: #222222;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --text-tertiary: #999999;
            --border-color: #333333;
            --border-dark: #555555;
            --success-color: #ffffff;
            --warning-color: #cccccc;
            --error-color: #ffffff;
        }

        /* Reset & Base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all var(--transition);
        }

        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-6);
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: var(--space-4);
            right: var(--space-4);
            background: var(--surface-elevated);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-3);
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: all var(--transition);
            z-index: 1000;
        }

        .theme-toggle:hover {
            background: var(--surface-hover);
            transform: scale(1.05);
        }

        .theme-toggle .material-icons {
            font-size: 24px;
            color: var(--text-primary);
        }

        /* Section Headers */
        .section-header {
            margin-bottom: var(--space-8);
        }

        .section-title {
            font-size: 32px;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
            letter-spacing: -0.02em;
        }

        .section-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            font-weight: 400;
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-12);
        }

        /* Product Card */
        .product-card {
            background: var(--surface);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-md);
            transition: all var(--transition-slow);
            position: relative;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--border-dark);
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        .product-header {
            text-align: center;
            margin-bottom: var(--space-6);
        }

        .product-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            line-height: 1.3;
        }

        .product-image {
            width: 120px;
            height: 120px;
            background: var(--surface-elevated);
            border-radius: var(--radius-lg);
            margin: 0 auto var(--space-4);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
            transition: all var(--transition);
        }

        .product-image:hover {
            transform: scale(1.05);
        }

        .product-image .material-icons {
            font-size: 48px;
            color: var(--text-secondary);
        }

        /* Warranty Badges */
        .warranty-badges {
            display: flex;
            gap: var(--space-2);
            justify-content: center;
            margin-bottom: var(--space-6);
            flex-wrap: wrap;
        }

        .warranty-badge {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid var(--border-color);
            transition: all var(--transition);
        }

        .warranty-badge.available {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .warranty-badge.guarantee {
            background: var(--surface-elevated);
            color: var(--text-primary);
            border-color: var(--border-dark);
        }

        .warranty-badge.no-warranty {
            background: var(--surface-elevated);
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        /* Warranty Information */
        .warranty-info {
            margin-bottom: var(--space-6);
        }

        .warranty-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            text-align: center;
        }

        .warranty-options {
            display: grid;
            gap: var(--space-3);
            margin-bottom: var(--space-6);
        }

        .warranty-option {
            background: var(--surface-elevated);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            cursor: pointer;
            transition: all var(--transition);
            position: relative;
        }

        .warranty-option:hover {
            border-color: var(--border-dark);
            background: var(--surface-hover);
        }

        .warranty-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .warranty-option-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: var(--space-1);
        }

        .warranty-option-details {
            font-size: 12px;
            opacity: 0.8;
            line-height: 1.4;
        }

        .warranty-option.selected .warranty-option-details {
            opacity: 0.9;
        }

        /* Product Actions */
        .product-actions {
            display: flex;
            gap: var(--space-3);
            margin-top: var(--space-6);
        }

        .btn {
            flex: 1;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--border-dark);
            border-radius: var(--radius-md);
            background: var(--surface);
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            text-decoration: none;
        }

        .btn:hover {
            background: var(--surface-hover);
            transform: translateY(-1px);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        .btn-secondary {
            background: var(--surface-elevated);
            border-color: var(--border-color);
        }

        /* Product Status */
        .product-status {
            text-align: center;
            margin-top: var(--space-4);
            padding: var(--space-3);
            background: var(--surface-elevated);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .status-text {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .status-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .status-link:hover {
            text-decoration: underline;
        }

        /* No Warranty Card */
        .no-warranty-card {
            text-align: center;
            padding: var(--space-6);
        }

        .no-warranty-text {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--space-6);
        }

        .direct-add-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
        }

        .direct-add-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Warranty Claims Section */
        .warranty-claims-section {
            margin-top: var(--space-12);
        }

        /* Search Filters */
        .search-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-8);
            padding: var(--space-6);
            background: var(--surface-elevated);
            border-radius: var(--radius-xl);
            border: 1px solid var(--border-color);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .search-input {
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--surface);
            color: var(--text-primary);
            font-size: 14px;
            transition: all var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] .search-input:focus {
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
        }

        /* Claims Table */
        .claims-table-container {
            background: var(--surface);
            border-radius: var(--radius-xl);
            border: 1px solid var(--border-color);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .claims-table {
            width: 100%;
        }

        .table-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 2fr 1fr;
            background: var(--surface-elevated);
            border-bottom: 2px solid var(--border-color);
        }

        .header-cell {
            padding: var(--space-4) var(--space-6);
            font-size: 14px;
            font-weight: 700;
            color: var(--text-primary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-right: 1px solid var(--border-color);
        }

        .header-cell:last-child {
            border-right: none;
        }

        .table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 2fr 1fr;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all var(--transition);
        }

        .table-row:hover {
            background: var(--surface-hover);
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .table-cell {
            padding: var(--space-4) var(--space-6);
            font-size: 14px;
            color: var(--text-primary);
            border-right: 1px solid var(--border-color);
            display: flex;
            align-items: center;
        }

        .table-cell:last-child {
            border-right: none;
        }

        /* Product Info */
        .product-info {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .product-info .material-icons {
            font-size: 20px;
            color: var(--text-secondary);
            background: var(--surface-elevated);
            padding: var(--space-2);
            border-radius: var(--radius-sm);
        }

        /* Status Progress */
        .status-progress {
            display: flex;
            gap: var(--space-2);
            align-items: center;
            flex-wrap: wrap;
        }

        .status-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-1);
            opacity: 0.4;
            transition: all var(--transition);
        }

        .status-step.active {
            opacity: 1;
        }

        .step-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid var(--border-color);
            transition: all var(--transition);
        }

        .status-step.active .step-indicator {
            border-color: var(--primary-color);
        }

        .step-indicator.submitted {
            background: var(--primary-color);
        }

        .step-indicator.review {
            background: var(--secondary-color);
        }

        .step-indicator.approved {
            background: var(--success-color);
        }

        .step-indicator.rejected {
            background: var(--error-color);
        }

        .step-indicator.closed {
            background: var(--text-tertiary);
        }

        .status-step span {
            font-size: 10px;
            font-weight: 500;
            color: var(--text-secondary);
            text-align: center;
        }

        .status-step.active span {
            color: var(--text-primary);
            font-weight: 600;
        }

        /* Resolution Badge */
        .resolution-badge {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .resolution-badge.pending {
            background: var(--surface-elevated);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .resolution-badge.replacement {
            background: var(--primary-color);
            color: white;
        }

        .resolution-badge.denied {
            background: var(--error-color);
            color: white;
        }

        .resolution-badge.refund {
            background: var(--success-color);
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: var(--space-4);
            }

            .section-title {
                font-size: 24px;
            }

            .products-grid {
                grid-template-columns: 1fr;
                gap: var(--space-4);
            }

            .search-filters {
                grid-template-columns: 1fr;
                gap: var(--space-3);
                padding: var(--space-4);
            }

            .table-header,
            .table-row {
                grid-template-columns: 1fr;
                gap: var(--space-2);
            }

            .table-cell {
                padding: var(--space-2) var(--space-3);
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }

            .table-cell:last-child {
                border-bottom: none;
            }

            .header-cell {
                padding: var(--space-2) var(--space-3);
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }

            .header-cell:last-child {
                border-bottom: none;
            }

            .status-progress {
                flex-direction: column;
                gap: var(--space-1);
            }

            .status-step {
                flex-direction: row;
                gap: var(--space-2);
            }

            .theme-toggle {
                top: var(--space-2);
                right: var(--space-2);
                padding: var(--space-2);
            }

            .product-actions {
                flex-direction: column;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: var(--space-2);
            }

            .section-title {
                font-size: 20px;
            }

            .product-card {
                padding: var(--space-4);
            }

            .warranty-option {
                padding: var(--space-3);
            }

            .search-filters {
                padding: var(--space-3);
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Theme">
        <span class="material-icons" id="themeIcon">dark_mode</span>
    </button>

    <div class="container">
        <!-- Products Section -->
        <section class="products-section">
            <div class="section-header">
                <h1 class="section-title">Products</h1>
                <p class="section-subtitle">Explore our premium product collection with comprehensive warranty options</p>
            </div>

            <div class="products-grid">
                <!-- Smart Oil Filter -->
                <div class="product-card">
                    <div class="product-header">
                        <h3 class="product-title">Smart Oil Filter with IoT Sensor</h3>
                        <div class="product-image">
                            <span class="material-icons">settings</span>
                        </div>
                    </div>

                    <div class="warranty-badges">
                        <span class="warranty-badge available">Warranty Available</span>
                        <span class="warranty-badge guarantee">30-Day Guarantee</span>
                    </div>

                    <div class="warranty-info">
                        <h4 class="warranty-title">Warranty Information</h4>
                        <p style="text-align: center; font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-4);">
                            Select Warranty Option: <span style="color: var(--primary-color); font-weight: 600;">*</span>
                        </p>

                        <div class="warranty-options">
                            <div class="warranty-option" onclick="selectWarranty(this, 'smart-oil-filter')">
                                <div class="warranty-option-title">Standard Warranty (90 Days)</div>
                                <div class="warranty-option-details">Basic coverage for manufacturing defects</div>
                            </div>
                            <div class="warranty-option" onclick="selectWarranty(this, 'smart-oil-filter')">
                                <div class="warranty-option-title">Extended Warranty (2 Years, Covers Defects + Accidental Damage)</div>
                                <div class="warranty-option-details">Comprehensive coverage including accidental damage protection</div>
                            </div>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary" onclick="addToCart('smart-oil-filter')">
                                <span class="material-icons">add_shopping_cart</span>
                                Add to Cart
                            </button>
                            <button class="btn btn-secondary" onclick="viewWarrantyPolicy('smart-oil-filter')">
                                <span class="material-icons">description</span>
                                Warranty Policy
                            </button>
                        </div>

                        <div class="product-status">
                            <p class="status-text">Product added to cart. <a href="#" class="status-link">View your account below.</a></p>
                        </div>
                    </div>
                </div>

                <!-- Google Nest Thermostat -->
                <div class="product-card">
                    <div class="product-header">
                        <h3 class="product-title">Google Nest Thermostat</h3>
                        <div class="product-image">
                            <span class="material-icons">thermostat</span>
                        </div>
                    </div>

                    <div class="warranty-badges">
                        <span class="warranty-badge available">Warranty Available</span>
                        <span class="warranty-badge guarantee">30-Day Guarantee</span>
                    </div>

                    <div class="warranty-info">
                        <h4 class="warranty-title">Warranty Information</h4>
                        <p style="text-align: center; font-size: 14px; color: var(--text-secondary); margin-bottom: var(--space-4);">
                            Select Warranty Option: <span style="color: var(--primary-color); font-weight: 600;">*</span>
                        </p>

                        <div class="warranty-options">
                            <div class="warranty-option" onclick="selectWarranty(this, 'nest-thermostat')">
                                <div class="warranty-option-title">Standard Warranty (1 Year)</div>
                                <div class="warranty-option-details">Standard manufacturer warranty coverage</div>
                            </div>
                            <div class="warranty-option" onclick="selectWarranty(this, 'nest-thermostat')">
                                <div class="warranty-option-title">Extended Warranty (2 Years, Covers Defects + Accidental Damage)</div>
                                <div class="warranty-option-details">Extended protection with accidental damage coverage</div>
                            </div>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary" onclick="addToCart('nest-thermostat')">
                                <span class="material-icons">add_shopping_cart</span>
                                Add to Cart
                            </button>
                            <button class="btn btn-secondary" onclick="viewWarrantyPolicy('nest-thermostat')">
                                <span class="material-icons">description</span>
                                Warranty Policy
                            </button>
                        </div>

                        <div class="product-status">
                            <p class="status-text">Product added to cart. <a href="#" class="status-link">View your account below.</a></p>
                        </div>
                    </div>
                </div>

                <!-- Basic Light Bulb -->
                <div class="product-card">
                    <div class="product-header">
                        <h3 class="product-title">Basic Light Bulb</h3>
                        <div class="product-image">
                            <span class="material-icons">lightbulb</span>
                        </div>
                    </div>

                    <div class="warranty-badges">
                        <span class="warranty-badge no-warranty">No Warranty Available</span>
                    </div>

                    <div class="no-warranty-card">
                        <p class="no-warranty-text">No Warranty Available</p>
                        <button class="direct-add-btn" onclick="addToCart('light-bulb')">
                            <span class="material-icons">add_shopping_cart</span>
                            Add to Cart
                        </button>
                        <div class="product-status">
                            <p class="status-text">Product added to cart. <a href="#" class="status-link">View your account below.</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Warranty Claim History Section -->
        <section class="warranty-claims-section">
            <div class="section-header">
                <h2 class="section-title">Warranty Claim History</h2>
                <p class="section-subtitle">Track and manage your warranty claims with real-time status updates</p>
            </div>

            <!-- Search Filters -->
            <div class="search-filters">
                <div class="filter-group">
                    <input type="text" class="search-input" placeholder="Search Product" id="searchProduct">
                </div>
                <div class="filter-group">
                    <input type="text" class="search-input" placeholder="Search Claim ID" id="searchClaimId">
                </div>
                <div class="filter-group">
                    <input type="date" class="search-input" placeholder="Search Claim Date" id="searchDate">
                </div>
                <div class="filter-group">
                    <input type="text" class="search-input" placeholder="Search Status" id="searchStatus">
                </div>
                <div class="filter-group">
                    <input type="text" class="search-input" placeholder="Search Resolution" id="searchResolution">
                </div>
            </div>

            <!-- Claims Table -->
            <div class="claims-table-container">
                <div class="claims-table">
                    <!-- Table Header -->
                    <div class="table-header">
                        <div class="header-cell">Product Name</div>
                        <div class="header-cell">Claim ID</div>
                        <div class="header-cell">Claim Date</div>
                        <div class="header-cell">Status</div>
                        <div class="header-cell">Resolution</div>
                    </div>

                    <!-- Table Rows -->
                    <div class="table-row" onclick="viewClaimDetails('CLM96765')">
                        <div class="table-cell">
                            <div class="product-info">
                                <span class="material-icons">settings</span>
                                <span>Smart Oil Filter with IoT Sensor</span>
                            </div>
                        </div>
                        <div class="table-cell">CLM96765</div>
                        <div class="table-cell">2025-05-15</div>
                        <div class="table-cell">
                            <div class="status-progress">
                                <div class="status-step active">
                                    <div class="step-indicator submitted"></div>
                                    <span>Submitted</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator review"></div>
                                    <span>Review</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator approved"></div>
                                    <span>Approved</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator rejected"></div>
                                    <span>Rejected</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator closed"></div>
                                    <span>Closed</span>
                                </div>
                            </div>
                        </div>
                        <div class="table-cell">
                            <span class="resolution-badge pending">Pending</span>
                        </div>
                    </div>

                    <!-- Google Nest Thermostat - Pending -->
                    <div class="table-row" onclick="viewClaimDetails('CLM54321')">
                        <div class="table-cell">
                            <div class="product-info">
                                <span class="material-icons">thermostat</span>
                                <span>Google Nest Thermostat</span>
                            </div>
                        </div>
                        <div class="table-cell">CLM54321</div>
                        <div class="table-cell">2025-05-01</div>
                        <div class="table-cell">
                            <div class="status-progress">
                                <div class="status-step active">
                                    <div class="step-indicator submitted"></div>
                                    <span>Submitted</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator review"></div>
                                    <span>Review</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator approved"></div>
                                    <span>Approved</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator rejected"></div>
                                    <span>Rejected</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator closed"></div>
                                    <span>Closed</span>
                                </div>
                            </div>
                        </div>
                        <div class="table-cell">
                            <span class="resolution-badge pending">Pending</span>
                        </div>
                    </div>

                    <!-- Google Nest Thermostat - Replacement -->
                    <div class="table-row" onclick="viewClaimDetails('CLM12345')">
                        <div class="table-cell">
                            <div class="product-info">
                                <span class="material-icons">thermostat</span>
                                <span>Google Nest Thermostat</span>
                            </div>
                        </div>
                        <div class="table-cell">CLM12345</div>
                        <div class="table-cell">2025-04-20</div>
                        <div class="table-cell">
                            <div class="status-progress">
                                <div class="status-step active">
                                    <div class="step-indicator submitted"></div>
                                    <span>Submitted</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator review"></div>
                                    <span>Review</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator approved"></div>
                                    <span>Approved</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator rejected"></div>
                                    <span>Rejected</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator closed"></div>
                                    <span>Closed</span>
                                </div>
                            </div>
                        </div>
                        <div class="table-cell">
                            <span class="resolution-badge replacement">Replacement</span>
                        </div>
                    </div>

                    <!-- Smart Oil Filter - Denied -->
                    <div class="table-row" onclick="viewClaimDetails('CLM67890')">
                        <div class="table-cell">
                            <div class="product-info">
                                <span class="material-icons">settings</span>
                                <span>Smart Oil Filter with IoT Sensor</span>
                            </div>
                        </div>
                        <div class="table-cell">CLM67890</div>
                        <div class="table-cell">2025-04-10</div>
                        <div class="table-cell">
                            <div class="status-progress">
                                <div class="status-step active">
                                    <div class="step-indicator submitted"></div>
                                    <span>Submitted</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator review"></div>
                                    <span>Review</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator approved"></div>
                                    <span>Approved</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator rejected"></div>
                                    <span>Rejected</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator closed"></div>
                                    <span>Closed</span>
                                </div>
                            </div>
                        </div>
                        <div class="table-cell">
                            <span class="resolution-badge denied">Denied</span>
                        </div>
                    </div>

                    <!-- Google Nest Thermostat - Refund -->
                    <div class="table-row" onclick="viewClaimDetails('CLM24680')">
                        <div class="table-cell">
                            <div class="product-info">
                                <span class="material-icons">thermostat</span>
                                <span>Google Nest Thermostat</span>
                            </div>
                        </div>
                        <div class="table-cell">CLM24680</div>
                        <div class="table-cell">2025-03-15</div>
                        <div class="table-cell">
                            <div class="status-progress">
                                <div class="status-step active">
                                    <div class="step-indicator submitted"></div>
                                    <span>Submitted</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator review"></div>
                                    <span>Review</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator approved"></div>
                                    <span>Approved</span>
                                </div>
                                <div class="status-step">
                                    <div class="step-indicator rejected"></div>
                                    <span>Rejected</span>
                                </div>
                                <div class="status-step active">
                                    <div class="step-indicator closed"></div>
                                    <span>Closed</span>
                                </div>
                            </div>
                        </div>
                        <div class="table-cell">
                            <span class="resolution-badge refund">Refund</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Theme Management
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);

            const themeIcon = document.getElementById('themeIcon');
            themeIcon.textContent = newTheme === 'dark' ? 'light_mode' : 'dark_mode';

            localStorage.setItem('theme', newTheme);
        }

        // Initialize theme
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const themeIcon = document.getElementById('themeIcon');
            themeIcon.textContent = savedTheme === 'dark' ? 'light_mode' : 'dark_mode';
        }

        // Warranty Selection
        function selectWarranty(element, productId) {
            // Remove selected class from all options in this product
            const productCard = element.closest('.product-card');
            const allOptions = productCard.querySelectorAll('.warranty-option');
            allOptions.forEach(option => option.classList.remove('selected'));

            // Add selected class to clicked option
            element.classList.add('selected');

            showNotification('Warranty option selected', 'success');
        }

        // Product Actions
        function addToCart(productId) {
            showNotification(`${getProductName(productId)} added to cart!`, 'success');
        }

        function viewWarrantyPolicy(productId) {
            showNotification(`Opening warranty policy for ${getProductName(productId)}`, 'info');
        }

        function viewClaimDetails(claimId) {
            showNotification(`Opening claim details for ${claimId}`, 'info');
        }

        // Helper Functions
        function getProductName(productId) {
            const names = {
                'smart-oil-filter': 'Smart Oil Filter with IoT Sensor',
                'nest-thermostat': 'Google Nest Thermostat',
                'light-bulb': 'Basic Light Bulb'
            };
            return names[productId] || 'Product';
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <span class="material-icons">
                    ${type === 'success' ? 'check_circle' : type === 'error' ? 'error' : 'info'}
                </span>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: inherit; cursor: pointer; margin-left: auto;">
                    <span class="material-icons">close</span>
                </button>
            `;

            // Add notification styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--surface-elevated);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                padding: 16px;
                box-shadow: var(--shadow-lg);
                display: flex;
                align-items: center;
                gap: 12px;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s ease;
                z-index: 1000;
                max-width: 350px;
                font-size: 14px;
                color: var(--text-primary);
            `;

            if (type === 'success') {
                notification.style.borderLeft = '4px solid var(--success-color)';
            } else if (type === 'error') {
                notification.style.borderLeft = '4px solid var(--error-color)';
            } else {
                notification.style.borderLeft = '4px solid var(--primary-color)';
            }

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 100);

            // Auto-hide notification
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentElement) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Search Functionality
        function initSearchFilters() {
            const searchInputs = document.querySelectorAll('.search-input');
            searchInputs.forEach(input => {
                input.addEventListener('input', filterClaims);
            });
        }

        function filterClaims() {
            const productFilter = document.getElementById('searchProduct').value.toLowerCase();
            const claimIdFilter = document.getElementById('searchClaimId').value.toLowerCase();
            const dateFilter = document.getElementById('searchDate').value;
            const statusFilter = document.getElementById('searchStatus').value.toLowerCase();
            const resolutionFilter = document.getElementById('searchResolution').value.toLowerCase();

            const rows = document.querySelectorAll('.table-row');

            rows.forEach(row => {
                const cells = row.querySelectorAll('.table-cell');
                const productName = cells[0].textContent.toLowerCase();
                const claimId = cells[1].textContent.toLowerCase();
                const claimDate = cells[2].textContent;
                const status = cells[3].textContent.toLowerCase();
                const resolution = cells[4].textContent.toLowerCase();

                const matchesProduct = !productFilter || productName.includes(productFilter);
                const matchesClaimId = !claimIdFilter || claimId.includes(claimIdFilter);
                const matchesDate = !dateFilter || claimDate.includes(dateFilter);
                const matchesStatus = !statusFilter || status.includes(statusFilter);
                const matchesResolution = !resolutionFilter || resolution.includes(resolutionFilter);

                if (matchesProduct && matchesClaimId && matchesDate && matchesStatus && matchesResolution) {
                    row.style.display = 'grid';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            initSearchFilters();
        });
    </script>
</body>
</html>
